package com.heartrate.monitor.camera

import android.content.Context
import androidx.camera.core.*
import androidx.camera.lifecycle.ProcessCameraProvider
import androidx.camera.view.PreviewView
import androidx.compose.foundation.layout.Box
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import java.util.concurrent.ExecutorService
import java.util.concurrent.Executors

@Composable
fun CameraPreview(
    modifier: Modifier = Modifier,
    onFrameAnalyzed: (FloatArray) -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val cameraExecutor = remember { Executors.newSingleThreadExecutor() }
    
    DisposableEffect(Unit) {
        onDispose {
            cameraExecutor.shutdown()
        }
    }
    
    Box(modifier = modifier) {
        AndroidView(
            factory = { ctx ->
                val previewView = PreviewView(ctx)
                startCamera(ctx, previewView, lifecycleOwner, cameraExecutor, onFrameAnalyzed)
                previewView
            }
        )
    }
}

private fun startCamera(
    context: Context,
    previewView: PreviewView,
    lifecycleOwner: androidx.lifecycle.LifecycleOwner,
    cameraExecutor: ExecutorService,
    onFrameAnalyzed: (FloatArray) -> Unit
) {
    val cameraProviderFuture = ProcessCameraProvider.getInstance(context)
    
    cameraProviderFuture.addListener({
        val cameraProvider = cameraProviderFuture.get()
        
        val preview = Preview.Builder().build()
        preview.setSurfaceProvider(previewView.surfaceProvider)
        
        val imageAnalyzer = ImageAnalysis.Builder()
            .setBackpressureStrategy(ImageAnalysis.STRATEGY_KEEP_ONLY_LATEST)
            .build()
        
        imageAnalyzer.setAnalyzer(cameraExecutor, HeartRateAnalyzer(onFrameAnalyzed))
        
        val cameraSelector = CameraSelector.DEFAULT_BACK_CAMERA
        
        try {
            cameraProvider.unbindAll()
            val camera = cameraProvider.bindToLifecycle(
                lifecycleOwner,
                cameraSelector,
                preview,
                imageAnalyzer
            )
            
            // 开启闪光灯
            camera.cameraControl.enableTorch(true)
            
        } catch (exc: Exception) {
            // 处理错误
        }
        
    }, ContextCompat.getMainExecutor(context))
}

private class HeartRateAnalyzer(
    private val onFrameAnalyzed: (FloatArray) -> Unit
) : ImageAnalysis.Analyzer {

    override fun analyze(image: ImageProxy) {
        val rgbValues = extractRGBValues(image)
        onFrameAnalyzed(rgbValues)
        image.close()
    }

    private fun extractRGBValues(image: ImageProxy): FloatArray {
        // 获取Y平面（亮度信息）
        val yBuffer = image.planes[0].buffer
        val ySize = yBuffer.remaining()
        val yArray = ByteArray(ySize)
        yBuffer.get(yArray)

        // 计算平均亮度值作为红色通道的近似
        var total = 0L
        for (byte in yArray) {
            total += (byte.toInt() and 0xFF)
        }

        val avgBrightness = total.toFloat() / yArray.size

        // 返回RGB值，这里主要使用亮度信息
        return floatArrayOf(
            avgBrightness, // R
            avgBrightness, // G
            avgBrightness  // B
        )
    }
}