package com.heartrate.monitor.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.material3.MaterialTheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.Path
import androidx.compose.ui.graphics.drawscope.DrawScope
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.unit.dp

@Composable
fun HeartRateChart(
    modifier: Modifier = Modifier,
    waveformData: List<Float>
) {
    val primaryColor = MaterialTheme.colorScheme.primary
    
    Canvas(
        modifier = modifier
            .background(MaterialTheme.colorScheme.surface)
            .padding(8.dp)
    ) {
        if (waveformData.isNotEmpty()) {
            drawWaveform(waveformData, primaryColor)
        }
    }
}

private fun DrawScope.drawWaveform(
    data: List<Float>,
    color: Color
) {
    if (data.size < 2) return
    
    val width = size.width
    val height = size.height
    val stepX = width / (data.size - 1)
    
    // 找到数据的最大值和最小值用于归一化
    val maxValue = data.maxOrNull() ?: 1f
    val minValue = data.minOrNull() ?: 0f
    val range = maxValue - minValue
    
    if (range == 0f) return
    
    val path = Path()
    
    // 归一化第一个点并开始路径
    val firstY = height - ((data[0] - minValue) / range) * height
    path.moveTo(0f, firstY)
    
    // 绘制其余点
    for (i in 1 until data.size) {
        val x = i * stepX
        val normalizedY = (data[i] - minValue) / range
        val y = height - normalizedY * height
        path.lineTo(x, y)
    }
    
    // 绘制路径
    drawPath(
        path = path,
        color = color,
        style = Stroke(width = 3.dp.toPx())
    )
    
    // 绘制网格线
    drawGridLines(color.copy(alpha = 0.3f))
}

private fun DrawScope.drawGridLines(color: Color) {
    val gridLines = 5
    val stepY = size.height / gridLines
    val stepX = size.width / gridLines
    
    // 水平网格线
    for (i in 0..gridLines) {
        val y = i * stepY
        drawLine(
            color = color,
            start = Offset(0f, y),
            end = Offset(size.width, y),
            strokeWidth = 1.dp.toPx()
        )
    }
    
    // 垂直网格线
    for (i in 0..gridLines) {
        val x = i * stepX
        drawLine(
            color = color,
            start = Offset(x, 0f),
            end = Offset(x, size.height),
            strokeWidth = 1.dp.toPx()
        )
    }
}
