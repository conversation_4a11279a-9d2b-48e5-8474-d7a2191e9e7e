{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fef0b4911f7fba22eaa6d4a2664b7d03\\transformed\\material-1.11.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,268,352,433,510,609,704,803,943,1026,1092,1187,1272,1334,1422,1484,1553,1616,1689,1752,1806,1927,1984,2046,2100,2177,2314,2399,2481,2616,2697,2778,2924,3015,3105,3160,3211,3277,3350,3430,3521,3601,3676,3753,3822,3899,4004,4092,4181,4274,4367,4441,4521,4615,4666,4750,4816,4900,4988,5050,5114,5177,5245,5360,5474,5580,5689,5748,5803", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "263,347,428,505,604,699,798,938,1021,1087,1182,1267,1329,1417,1479,1548,1611,1684,1747,1801,1922,1979,2041,2095,2172,2309,2394,2476,2611,2692,2773,2919,3010,3100,3155,3206,3272,3345,3425,3516,3596,3671,3748,3817,3894,3999,4087,4176,4269,4362,4436,4516,4610,4661,4745,4811,4895,4983,5045,5109,5172,5240,5355,5469,5575,5684,5743,5798,5878"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3493,3577,3658,3735,3834,4676,4775,4915,9271,9422,9517,9787,9849,9937,9999,10068,10131,10204,10267,10321,10442,10499,10561,10615,10692,10829,10914,10996,11131,11212,11293,11439,11530,11620,11675,11726,11792,11865,11945,12036,12116,12191,12268,12337,12414,12519,12607,12696,12789,12882,12956,13036,13130,13181,13265,13331,13415,13503,13565,13629,13692,13760,13875,13989,14095,14204,14263,14635", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,83,80,76,98,94,98,139,82,65,94,84,61,87,61,68,62,72,62,53,120,56,61,53,76,136,84,81,134,80,80,145,90,89,54,50,65,72,79,90,79,74,76,68,76,104,87,88,92,92,73,79,93,50,83,65,83,87,61,63,62,67,114,113,105,108,58,54,79", "endOffsets": "313,3572,3653,3730,3829,3924,4770,4910,4993,9332,9512,9597,9844,9932,9994,10063,10126,10199,10262,10316,10437,10494,10556,10610,10687,10824,10909,10991,11126,11207,11288,11434,11525,11615,11670,11721,11787,11860,11940,12031,12111,12186,12263,12332,12409,12514,12602,12691,12784,12877,12951,13031,13125,13176,13260,13326,13410,13498,13560,13624,13687,13755,13870,13984,14090,14199,14258,14313,14710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff3e7a6cbd6877b655429719bd7e51c8\\transformed\\appcompat-1.6.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,883,975,1069,1162,1256,1357,1451,1548,1643,1735,1827,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "205,308,417,501,606,725,803,878,970,1064,1157,1251,1352,1446,1543,1638,1730,1822,1903,2009,2116,2214,2318,2424,2531,2694,2794,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "318,423,526,635,719,824,943,1021,1096,1188,1282,1375,1469,1570,1664,1761,1856,1948,2040,2121,2227,2334,2432,2536,2642,2749,2912,14969", "endColumns": "104,102,108,83,104,118,77,74,91,93,92,93,100,93,96,94,91,91,80,105,106,97,103,105,106,162,99,81", "endOffsets": "418,521,630,714,819,938,1016,1091,1183,1277,1370,1464,1565,1659,1756,1851,1943,2035,2116,2222,2329,2427,2531,2637,2744,2907,3007,15046"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,178,298,416,536,624,716,826,965,1080,1234,1314,1412,1502,1598,1713,1831,1944,2082,2218,2357,2529,2660,2776,2896,3023,3114,3207,3330,3464,3560,3666,3768,3906,4050,4156,4252,4337,4422,4504,4586,4685,4761,4840,4935,5034,5121,5215,5299,5405,5501,5599,5713,5789,5899", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "173,293,411,531,619,711,821,960,1075,1229,1309,1407,1497,1593,1708,1826,1939,2077,2213,2352,2524,2655,2771,2891,3018,3109,3202,3325,3459,3555,3661,3763,3901,4045,4151,4247,4332,4417,4499,4581,4680,4756,4835,4930,5029,5116,5210,5294,5400,5496,5594,5708,5784,5894,5997"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3012,3135,3255,3373,5185,5273,5365,5475,5614,5729,5883,5963,6061,6151,6247,6362,6480,6593,6731,6867,7006,7178,7309,7425,7545,7672,7763,7856,7979,8113,8209,8315,8417,8555,8699,8805,9099,9337,14887,15136,15319,15686,15762,15841,15936,16035,16122,16216,16300,16406,16502,16600,16714,16790,16900", "endColumns": "122,119,117,119,87,91,109,138,114,153,79,97,89,95,114,117,112,137,135,138,171,130,115,119,126,90,92,122,133,95,105,101,137,143,105,95,84,84,81,81,98,75,78,94,98,86,93,83,105,95,97,113,75,109,102", "endOffsets": "3130,3250,3368,3488,5268,5360,5470,5609,5724,5878,5958,6056,6146,6242,6357,6475,6588,6726,6862,7001,7173,7304,7420,7540,7667,7758,7851,7974,8108,8204,8310,8412,8550,8694,8800,8896,9179,9417,14964,15213,15413,15757,15836,15931,16030,16117,16211,16295,16401,16497,16595,16709,16785,16895,16998"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3929,4027,4129,4228,4330,4439,4546,15218", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "4022,4124,4223,4325,4434,4541,4671,15314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1015,1079,1163,1251,1336,1414,1483", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1010,1074,1158,1246,1331,1409,1478,1599"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4998,5098,8901,8999,9184,9602,9681,14318,14411,14506,14571,14715,14799,15051,15418,15496,15565", "endColumns": "99,86,97,99,86,78,105,92,94,64,63,83,87,84,77,68,120", "endOffsets": "5093,5180,8994,9094,9266,9676,9782,14406,14501,14566,14630,14794,14882,15131,15491,15560,15681"}}]}]}