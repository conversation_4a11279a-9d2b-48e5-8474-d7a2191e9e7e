{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-ms/values-ms.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ccd268e0ca2e1cd10ec650ac8067a3e\\transformed\\core-1.12.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,459,565,683,798", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "145,247,344,454,560,678,793,894"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3905,4000,4102,4199,4309,4415,4533,14913", "endColumns": "94,101,96,109,105,117,114,100", "endOffsets": "3995,4097,4194,4304,4410,4528,4643,15009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a42b12ad0a51f8e308439dbb8e147289\\transformed\\material3-1.1.2\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,286,396,511,591,692,811,957,1081,1225,1307,1403,1491,1585,1697,1819,1920,2054,2185,2313,2488,2609,2730,2855,2981,3070,3167,3284,3408,3505,3608,3710,3846,3988,4091,4185,4257,4337,4420,4505,4603,4679,4758,4853,4948,5041,5140,5223,5322,5417,5519,5637,5714,5816", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "166,281,391,506,586,687,806,952,1076,1220,1302,1398,1486,1580,1692,1814,1915,2049,2180,2308,2483,2604,2725,2850,2976,3065,3162,3279,3403,3500,3603,3705,3841,3983,4086,4180,4252,4332,4415,4500,4598,4674,4753,4848,4943,5036,5135,5218,5317,5412,5514,5632,5709,5811,5903"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3140,3255,3365,5124,5204,5305,5424,5570,5694,5838,5920,6016,6104,6198,6310,6432,6533,6667,6798,6926,7101,7222,7343,7468,7594,7683,7780,7897,8021,8118,8221,8323,8459,8601,8704,8996,9218,14592,14828,15014,15374,15450,15529,15624,15719,15812,15911,15994,16093,16188,16290,16408,16485,16587", "endColumns": "115,114,109,114,79,100,118,145,123,143,81,95,87,93,111,121,100,133,130,127,174,120,120,124,125,88,96,116,123,96,102,101,135,141,102,93,71,79,82,84,97,75,78,94,94,92,98,82,98,94,101,117,76,101,91", "endOffsets": "3135,3250,3360,3475,5199,5300,5419,5565,5689,5833,5915,6011,6099,6193,6305,6427,6528,6662,6793,6921,7096,7217,7338,7463,7589,7678,7775,7892,8016,8113,8216,8318,8454,8596,8699,8793,9063,9293,14670,14908,15107,15445,15524,15619,15714,15807,15906,15989,16088,16183,16285,16403,16480,16582,16674"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\184958b59b0b0fbc0694ac3308e227ed\\transformed\\material-1.11.0\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,279,359,438,525,617,704,807,923,1006,1071,1164,1229,1288,1375,1437,1499,1559,1625,1687,1741,1849,1906,1967,2022,2093,2213,2304,2390,2538,2624,2710,2838,2926,3004,3057,3108,3174,3245,3323,3406,3485,3558,3634,3707,3778,3885,3977,4050,4140,4233,4307,4378,4469,4521,4601,4669,4753,4838,4900,4964,5027,5099,5203,5311,5407,5513,5570,5625", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "274,354,433,520,612,699,802,918,1001,1066,1159,1224,1283,1370,1432,1494,1554,1620,1682,1736,1844,1901,1962,2017,2088,2208,2299,2385,2533,2619,2705,2833,2921,2999,3052,3103,3169,3240,3318,3401,3480,3553,3629,3702,3773,3880,3972,4045,4135,4228,4302,4373,4464,4516,4596,4664,4748,4833,4895,4959,5022,5094,5198,5306,5402,5508,5565,5620,5706"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3480,3560,3639,3726,3818,4648,4751,4867,9153,9298,9391,9634,9693,9780,9842,9904,9964,10030,10092,10146,10254,10311,10372,10427,10498,10618,10709,10795,10943,11029,11115,11243,11331,11409,11462,11513,11579,11650,11728,11811,11890,11963,12039,12112,12183,12290,12382,12455,12545,12638,12712,12783,12874,12926,13006,13074,13158,13243,13305,13369,13432,13504,13608,13716,13812,13918,13975,14335", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,79,78,86,91,86,102,115,82,64,92,64,58,86,61,61,59,65,61,53,107,56,60,54,70,119,90,85,147,85,85,127,87,77,52,50,65,70,77,82,78,72,75,72,70,106,91,72,89,92,73,70,90,51,79,67,83,84,61,63,62,71,103,107,95,105,56,54,85", "endOffsets": "324,3555,3634,3721,3813,3900,4746,4862,4945,9213,9386,9451,9688,9775,9837,9899,9959,10025,10087,10141,10249,10306,10367,10422,10493,10613,10704,10790,10938,11024,11110,11238,11326,11404,11457,11508,11574,11645,11723,11806,11885,11958,12034,12107,12178,12285,12377,12450,12540,12633,12707,12778,12869,12921,13001,13069,13153,13238,13300,13364,13427,13499,13603,13711,13807,13913,13970,14025,14416"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af226701e07b6e5753175ffad1df9a85\\transformed\\appcompat-1.6.1\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,321,429,516,620,731,810,888,979,1072,1167,1261,1359,1452,1547,1641,1732,1823,1903,2015,2123,2220,2329,2433,2540,2699,2800", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "211,316,424,511,615,726,805,883,974,1067,1162,1256,1354,1447,1542,1636,1727,1818,1898,2010,2118,2215,2324,2428,2535,2694,2795,2876"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "329,440,545,653,740,844,955,1034,1112,1203,1296,1391,1485,1583,1676,1771,1865,1956,2047,2127,2239,2347,2444,2553,2657,2764,2923,14675", "endColumns": "110,104,107,86,103,110,78,77,90,92,94,93,97,92,94,93,90,90,79,111,107,96,108,103,106,158,100,80", "endOffsets": "435,540,648,735,839,950,1029,1107,1198,1291,1386,1480,1578,1671,1766,1860,1951,2042,2122,2234,2342,2439,2548,2652,2759,2918,3019,14751"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d309ccf62c6e6afb4fd21dd867c4c64e\\transformed\\ui-release\\res\\values-ms\\values-ms.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,279,375,477,562,645,740,827,912,978,1045,1130,1216,1288,1364,1430", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "190,274,370,472,557,640,735,822,907,973,1040,1125,1211,1283,1359,1425,1545"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4950,5040,8798,8894,9068,9456,9539,14030,14117,14202,14268,14421,14506,14756,15112,15188,15254", "endColumns": "89,83,95,101,84,82,94,86,84,65,66,84,85,71,75,65,119", "endOffsets": "5035,5119,8889,8991,9148,9534,9629,14112,14197,14263,14330,14501,14587,14823,15183,15249,15369"}}]}]}