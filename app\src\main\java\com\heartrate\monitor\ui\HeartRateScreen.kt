package com.heartrate.monitor.ui

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import com.heartrate.monitor.camera.CameraPreview
import com.heartrate.monitor.ui.components.HeartRateChart
import com.heartrate.monitor.ui.components.MeasurementControls
import com.heartrate.monitor.viewmodel.HeartRateViewModel

@Composable
fun HeartRateScreen(
    viewModel: HeartRateViewModel = viewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // 摄像头预览
        CameraPreview(
            modifier = Modifier
                .size(200.dp),
            onFrameAnalyzed = { rgbValues ->
                viewModel.processFrame(rgbValues)
            }
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 心率显示
        Card(
            modifier = Modifier.fillMaxWidth()
        ) {
            Column(
                modifier = Modifier.padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Text(
                    text = "心率: ${uiState.heartRate} BPM",
                    style = MaterialTheme.typography.headlineMedium
                )
                Text(
                    text = "状态: ${uiState.status}",
                    style = MaterialTheme.typography.bodyMedium
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 波形图
        HeartRateChart(
            modifier = Modifier
                .fillMaxWidth()
                .height(200.dp),
            waveformData = uiState.waveformData
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // 控制按钮
        MeasurementControls(
            isRecording = uiState.isRecording,
            onStartStop = { viewModel.toggleRecording() },
            onExport = { viewModel.exportData() }
        )
    }
}
