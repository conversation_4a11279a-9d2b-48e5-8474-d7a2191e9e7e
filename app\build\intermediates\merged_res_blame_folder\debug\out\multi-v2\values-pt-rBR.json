{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-pt-rBR/values-pt-rBR.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,2843", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,2923"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,331,438,527,628,747,832,913,1004,1096,1191,1285,1386,1479,1574,1669,1760,1851,1935,2042,2153,2255,2363,2471,2581,2743,9048", "endColumns": "119,105,106,88,100,118,84,80,90,91,94,93,100,92,94,94,90,90,83,106,110,101,107,107,109,161,99,84", "endOffsets": "220,326,433,522,623,742,827,908,999,1091,1186,1280,1381,1474,1569,1664,1755,1846,1930,2037,2148,2250,2358,2466,2576,2738,2838,9128"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,170,287,399,511,587,679,789,919,1033,1180,1260,1358,1449,1545,1656,1782,1885,2020,2154,2290,2452,2584,2700,2821,2945,3037,3130,3246,3358,3454,3561,3666,3802,3943,4049,4147,4229,4303,4388,4473,4570,4646,4726,4823,4925,5013,5108,5192,5300,5397,5496,5611,5687,5783", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "165,282,394,506,582,674,784,914,1028,1175,1255,1353,1444,1540,1651,1777,1880,2015,2149,2285,2447,2579,2695,2816,2940,3032,3125,3241,3353,3449,3556,3661,3797,3938,4044,4142,4224,4298,4383,4468,4565,4641,4721,4818,4920,5008,5103,5187,5295,5392,5491,5606,5682,5778,5866"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2843,2958,3075,3187,4215,4291,4383,4493,4623,4737,4884,4964,5062,5153,5249,5360,5486,5589,5724,5858,5994,6156,6288,6404,6525,6649,6741,6834,6950,7062,7158,7265,7370,7506,7647,7753,8047,8215,8963,9209,9395,9748,9824,9904,10001,10103,10191,10286,10370,10478,10575,10674,10789,10865,10961", "endColumns": "114,116,111,111,75,91,109,129,113,146,79,97,90,95,110,125,102,134,133,135,161,131,115,120,123,91,92,115,111,95,106,104,135,140,105,97,81,73,84,84,96,75,79,96,101,87,94,83,107,96,98,114,75,95,87", "endOffsets": "2953,3070,3182,3294,4286,4378,4488,4618,4732,4879,4959,5057,5148,5244,5355,5481,5584,5719,5853,5989,6151,6283,6399,6520,6644,6736,6829,6945,7057,7153,7260,7365,7501,7642,7748,7846,8124,8284,9043,9289,9487,9819,9899,9996,10098,10186,10281,10365,10473,10570,10669,10784,10860,10956,11044"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,152,254,353,453,560,670,790", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "147,249,348,448,555,665,785,886"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3299,3396,3498,3597,3697,3804,3914,9294", "endColumns": "96,101,98,99,106,109,119,100", "endOffsets": "3391,3493,3592,3692,3799,3909,4029,9390"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-pt-rBR\\values-pt-rBR.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,286,383,482,568,651,748,839,926,998,1067,1152,1242,1318,1394,1461", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "195,281,378,477,563,646,743,834,921,993,1062,1147,1237,1313,1389,1456,1569"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4034,4129,7851,7948,8129,8289,8372,8469,8560,8647,8719,8788,8873,9133,9492,9568,9635", "endColumns": "94,85,96,98,85,82,96,90,86,71,68,84,89,75,75,66,112", "endOffsets": "4124,4210,7943,8042,8210,8367,8464,8555,8642,8714,8783,8868,8958,9204,9563,9630,9743"}}]}]}