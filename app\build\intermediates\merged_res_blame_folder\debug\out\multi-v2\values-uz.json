{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-uz/values-uz.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ccd268e0ca2e1cd10ec650ac8067a3e\\transformed\\core-1.12.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,157,259,360,460,568,672,791", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "152,254,355,455,563,667,786,887"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3869,3971,4073,4174,4274,4382,4486,14871", "endColumns": "101,101,100,99,107,103,118,100", "endOffsets": "3966,4068,4169,4269,4377,4481,4600,14967"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a42b12ad0a51f8e308439dbb8e147289\\transformed\\material3-1.1.2\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,389,499,577,671,783,912,1017,1152,1232,1327,1417,1511,1621,1738,1843,1964,2083,2209,2373,2494,2611,2732,2850,2941,3035,3148,3270,3370,3476,3579,3697,3821,3930,4029,4109,4185,4269,4351,4448,4524,4604,4700,4800,4892,4987,5071,5175,5271,5369,5504,5580,5692", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "163,274,384,494,572,666,778,907,1012,1147,1227,1322,1412,1506,1616,1733,1838,1959,2078,2204,2368,2489,2606,2727,2845,2936,3030,3143,3265,3365,3471,3574,3692,3816,3925,4024,4104,4180,4264,4346,4443,4519,4599,4695,4795,4887,4982,5066,5170,5266,5364,5499,5575,5687,5786"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3004,3117,3228,3338,5095,5173,5267,5379,5508,5613,5748,5828,5923,6013,6107,6217,6334,6439,6560,6679,6805,6969,7090,7207,7328,7446,7537,7631,7744,7866,7966,8072,8175,8293,8417,8526,8836,9076,14547,14789,14972,15337,15413,15493,15589,15689,15781,15876,15960,16064,16160,16258,16393,16469,16581", "endColumns": "112,110,109,109,77,93,111,128,104,134,79,94,89,93,109,116,104,120,118,125,163,120,116,120,117,90,93,112,121,99,105,102,117,123,108,98,79,75,83,81,96,75,79,95,99,91,94,83,103,95,97,134,75,111,98", "endOffsets": "3112,3223,3333,3443,5168,5262,5374,5503,5608,5743,5823,5918,6008,6102,6212,6329,6434,6555,6674,6800,6964,7085,7202,7323,7441,7532,7626,7739,7861,7961,8067,8170,8288,8412,8521,8620,8911,9147,14626,14866,15064,15408,15488,15584,15684,15776,15871,15955,16059,16155,16253,16388,16464,16576,16675"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d309ccf62c6e6afb4fd21dd867c4c64e\\transformed\\ui-release\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,289,393,500,596,679,769,862,945,1013,1080,1161,1244,1318,1401,1469", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "199,284,388,495,591,674,764,857,940,1008,1075,1156,1239,1313,1396,1464,1581"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4911,5010,8625,8729,8916,9325,9408,13989,14082,14165,14233,14383,14464,14715,15069,15152,15220", "endColumns": "98,84,103,106,95,82,89,92,82,67,66,80,82,73,82,67,116", "endOffsets": "5005,5090,8724,8831,9007,9403,9493,14077,14160,14228,14295,14459,14542,14784,15147,15215,15332"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af226701e07b6e5753175ffad1df9a85\\transformed\\appcompat-1.6.1\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,305,405,487,587,704,789,867,958,1051,1146,1240,1334,1427,1522,1617,1708,1800,1884,1994,2100,2200,2308,2414,2516,2677,2776", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "205,300,400,482,582,699,784,862,953,1046,1141,1235,1329,1422,1517,1612,1703,1795,1879,1989,2095,2195,2303,2409,2511,2672,2771,2855"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "333,438,533,633,715,815,932,1017,1095,1186,1279,1374,1468,1562,1655,1750,1845,1936,2028,2112,2222,2328,2428,2536,2642,2744,2905,14631", "endColumns": "104,94,99,81,99,116,84,77,90,92,94,93,93,92,94,94,90,91,83,109,105,99,107,105,101,160,98,83", "endOffsets": "433,528,628,710,810,927,1012,1090,1181,1274,1369,1463,1557,1650,1745,1840,1931,2023,2107,2217,2323,2423,2531,2637,2739,2900,2999,14710"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\184958b59b0b0fbc0694ac3308e227ed\\transformed\\material-1.11.0\\res\\values-uz\\values-uz.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,283,360,440,523,617,704,799,926,1010,1074,1177,1247,1314,1423,1486,1553,1612,1686,1749,1803,1918,1976,2038,2092,2167,2296,2386,2475,2616,2698,2780,2919,3005,3089,3149,3200,3266,3339,3417,3503,3584,3656,3733,3808,3879,3980,4074,4153,4249,4343,4417,4493,4579,4632,4719,4785,4870,4961,5023,5087,5150,5219,5321,5422,5518,5619,5683,5738", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "278,355,435,518,612,699,794,921,1005,1069,1172,1242,1309,1418,1481,1548,1607,1681,1744,1798,1913,1971,2033,2087,2162,2291,2381,2470,2611,2693,2775,2914,3000,3084,3144,3195,3261,3334,3412,3498,3579,3651,3728,3803,3874,3975,4069,4148,4244,4338,4412,4488,4574,4627,4714,4780,4865,4956,5018,5082,5145,5214,5316,5417,5513,5614,5678,5733,5816"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3448,3525,3605,3688,3782,4605,4700,4827,9012,9152,9255,9498,9565,9674,9737,9804,9863,9937,10000,10054,10169,10227,10289,10343,10418,10547,10637,10726,10867,10949,11031,11170,11256,11340,11400,11451,11517,11590,11668,11754,11835,11907,11984,12059,12130,12231,12325,12404,12500,12594,12668,12744,12830,12883,12970,13036,13121,13212,13274,13338,13401,13470,13572,13673,13769,13870,13934,14300", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,76,79,82,93,86,94,126,83,63,102,69,66,108,62,66,58,73,62,53,114,57,61,53,74,128,89,88,140,81,81,138,85,83,59,50,65,72,77,85,80,71,76,74,70,100,93,78,95,93,73,75,85,52,86,65,84,90,61,63,62,68,101,100,95,100,63,54,82", "endOffsets": "328,3520,3600,3683,3777,3864,4695,4822,4906,9071,9250,9320,9560,9669,9732,9799,9858,9932,9995,10049,10164,10222,10284,10338,10413,10542,10632,10721,10862,10944,11026,11165,11251,11335,11395,11446,11512,11585,11663,11749,11830,11902,11979,12054,12125,12226,12320,12399,12495,12589,12663,12739,12825,12878,12965,13031,13116,13207,13269,13333,13396,13465,13567,13668,13764,13865,13929,13984,14378"}}]}]}