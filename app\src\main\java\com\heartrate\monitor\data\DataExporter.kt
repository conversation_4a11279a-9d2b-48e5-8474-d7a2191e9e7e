package com.heartrate.monitor.data

import android.content.Context
import android.os.Environment
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import java.io.File
import java.io.FileWriter
import java.text.SimpleDateFormat
import java.util.*

class DataExporter {
    
    suspend fun exportToCSV(data: List<HeartRateData>): String = withContext(Dispatchers.IO) {
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "heartrate_data_$timestamp.csv"
        
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        val file = File(downloadsDir, fileName)
        
        FileWriter(file).use { writer ->
            // 写入标题行
            writer.append("Timestamp,RedValue,GreenValue,BlueValue,DateTime\n")
            
            // 写入数据
            data.forEach { dataPoint ->
                val dateTime = SimpleDateFormat("yyyy-MM-dd HH:mm:ss.SSS", Locale.getDefault())
                    .format(Date(dataPoint.timestamp))
                
                writer.append("${dataPoint.timestamp},")
                writer.append("${dataPoint.redValue},")
                writer.append("${dataPoint.greenValue},")
                writer.append("${dataPoint.blueValue},")
                writer.append("$dateTime\n")
            }
        }
        
        file.absolutePath
    }
    
    suspend fun exportToJSON(data: List<HeartRateData>): String = withContext(Dispatchers.IO) {
        // JSON导出实现
        val timestamp = SimpleDateFormat("yyyyMMdd_HHmmss", Locale.getDefault()).format(Date())
        val fileName = "heartrate_data_$timestamp.json"
        
        val downloadsDir = Environment.getExternalStoragePublicDirectory(Environment.DIRECTORY_DOWNLOADS)
        val file = File(downloadsDir, fileName)
        
        // 简单的JSON格式化
        val jsonContent = buildString {
            append("{\n")
            append("  \"exportTime\": \"${Date()}\",\n")
            append("  \"dataCount\": ${data.size},\n")
            append("  \"data\": [\n")
            
            data.forEachIndexed { index, dataPoint ->
                append("    {\n")
                append("      \"timestamp\": ${dataPoint.timestamp},\n")
                append("      \"redValue\": ${dataPoint.redValue},\n")
                append("      \"greenValue\": ${dataPoint.greenValue},\n")
                append("      \"blueValue\": ${dataPoint.blueValue}\n")
                append("    }")
                if (index < data.size - 1) append(",")
                append("\n")
            }
            
            append("  ]\n")
            append("}")
        }
        
        file.writeText(jsonContent)
        file.absolutePath
    }
}