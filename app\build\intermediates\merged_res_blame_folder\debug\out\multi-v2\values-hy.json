{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-hy/values-hy.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fef0b4911f7fba22eaa6d4a2664b7d03\\transformed\\material-1.11.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,265,341,417,497,589,677,772,902,983,1047,1144,1229,1291,1378,1440,1504,1565,1632,1693,1747,1869,1926,1986,2040,2121,2256,2340,2425,2561,2636,2711,2854,2949,3029,3085,3138,3204,3278,3357,3443,3526,3597,3673,3749,3826,3932,4020,4100,4196,4292,4366,4444,4544,4595,4679,4748,4835,4926,4988,5052,5115,5186,5291,5397,5497,5600,5660,5717", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "260,336,412,492,584,672,767,897,978,1042,1139,1224,1286,1373,1435,1499,1560,1627,1688,1742,1864,1921,1981,2035,2116,2251,2335,2420,2556,2631,2706,2849,2944,3024,3080,3133,3199,3273,3352,3438,3521,3592,3668,3744,3821,3927,4015,4095,4191,4287,4361,4439,4539,4590,4674,4743,4830,4921,4983,5047,5110,5181,5286,5392,5492,5595,5655,5712,5797"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3470,3546,3622,3702,3794,4602,4697,4827,9145,9287,9384,9637,9699,9786,9848,9912,9973,10040,10101,10155,10277,10334,10394,10448,10529,10664,10748,10833,10969,11044,11119,11262,11357,11437,11493,11546,11612,11686,11765,11851,11934,12005,12081,12157,12234,12340,12428,12508,12604,12700,12774,12852,12952,13003,13087,13156,13243,13334,13396,13460,13523,13594,13699,13805,13905,14008,14068,14445", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,75,75,79,91,87,94,129,80,63,96,84,61,86,61,63,60,66,60,53,121,56,59,53,80,134,83,84,135,74,74,142,94,79,55,52,65,73,78,85,82,70,75,75,76,105,87,79,95,95,73,77,99,50,83,68,86,90,61,63,62,70,104,105,99,102,59,56,84", "endOffsets": "310,3541,3617,3697,3789,3877,4692,4822,4903,9204,9379,9464,9694,9781,9843,9907,9968,10035,10096,10150,10272,10329,10389,10443,10524,10659,10743,10828,10964,11039,11114,11257,11352,11432,11488,11541,11607,11681,11760,11846,11929,12000,12076,12152,12229,12335,12423,12503,12599,12695,12769,12847,12947,12998,13082,13151,13238,13329,13391,13455,13518,13589,13694,13800,13900,14003,14063,14120,14525"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,380,480,563,645,731,826,908,980,1051,1136,1224,1298,1379,1448", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "199,281,375,475,558,640,726,821,903,975,1046,1131,1219,1293,1374,1443,1561"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4908,5007,8781,8875,9062,9469,9551,14125,14220,14302,14374,14530,14615,14870,15236,15317,15386", "endColumns": "98,81,93,99,82,81,85,94,81,71,70,84,87,73,80,68,117", "endOffsets": "5002,5084,8870,8970,9140,9546,9632,14215,14297,14369,14440,14610,14698,14939,15312,15381,15499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,155,260,358,457,562,664,775", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "150,255,353,452,557,659,770,871"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3882,3982,4087,4185,4284,4389,4491,15025", "endColumns": "99,104,97,98,104,101,110,100", "endOffsets": "3977,4082,4180,4279,4384,4486,4597,15121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff3e7a6cbd6877b655429719bd7e51c8\\transformed\\appcompat-1.6.1\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,313,423,512,618,735,817,897,988,1081,1176,1270,1370,1463,1558,1652,1743,1834,1917,2023,2129,2228,2338,2446,2547,2717,2814", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "208,308,418,507,613,730,812,892,983,1076,1171,1265,1365,1458,1553,1647,1738,1829,1912,2018,2124,2223,2333,2441,2542,2712,2809,2892"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "315,423,523,633,722,828,945,1027,1107,1198,1291,1386,1480,1580,1673,1768,1862,1953,2044,2127,2233,2339,2438,2548,2656,2757,2927,14787", "endColumns": "107,99,109,88,105,116,81,79,90,92,94,93,99,92,94,93,90,90,82,105,105,98,109,107,100,169,96,82", "endOffsets": "418,518,628,717,823,940,1022,1102,1193,1286,1381,1475,1575,1668,1763,1857,1948,2039,2122,2228,2334,2433,2543,2651,2752,2922,3019,14865"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-hy\\values-hy.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,167,278,389,501,578,676,792,934,1053,1205,1288,1398,1489,1584,1702,1820,1923,2059,2193,2322,2495,2620,2732,2848,2968,3060,3154,3273,3410,3512,3613,3717,3851,3991,4096,4193,4280,4358,4442,4523,4633,4709,4788,4883,4980,5067,5159,5241,5341,5435,5530,5643,5719,5820", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "162,273,384,496,573,671,787,929,1048,1200,1283,1393,1484,1579,1697,1815,1918,2054,2188,2317,2490,2615,2727,2843,2963,3055,3149,3268,3405,3507,3608,3712,3846,3986,4091,4188,4275,4353,4437,4518,4628,4704,4783,4878,4975,5062,5154,5236,5336,5430,5525,5638,5714,5815,5905"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3024,3136,3247,3358,5089,5166,5264,5380,5522,5641,5793,5876,5986,6077,6172,6290,6408,6511,6647,6781,6910,7083,7208,7320,7436,7556,7648,7742,7861,7998,8100,8201,8305,8439,8579,8684,8975,9209,14703,14944,15126,15504,15580,15659,15754,15851,15938,16030,16112,16212,16306,16401,16514,16590,16691", "endColumns": "111,110,110,111,76,97,115,141,118,151,82,109,90,94,117,117,102,135,133,128,172,124,111,115,119,91,93,118,136,101,100,103,133,139,104,96,86,77,83,80,109,75,78,94,96,86,91,81,99,93,94,112,75,100,89", "endOffsets": "3131,3242,3353,3465,5161,5259,5375,5517,5636,5788,5871,5981,6072,6167,6285,6403,6506,6642,6776,6905,7078,7203,7315,7431,7551,7643,7737,7856,7993,8095,8196,8300,8434,8574,8679,8776,9057,9282,14782,15020,15231,15575,15654,15749,15846,15933,16025,16107,16207,16301,16396,16509,16585,16686,16776"}}]}]}