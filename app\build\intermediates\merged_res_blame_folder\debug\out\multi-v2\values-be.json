{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-be/values-be.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,912,1004,1097,1192,1286,1382,1476,1572,1667,1759,1851,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,77,91,92,94,93,95,93,95,94,91,91,79,105,104,97,107,105,107,172,99,80", "endOffsets": "220,323,439,525,630,749,829,907,999,1092,1187,1281,1377,1471,1567,1662,1754,1846,1926,2032,2137,2235,2343,2449,2557,2730,2830,2911"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,912,1004,1097,1192,1286,1382,1476,1572,1667,1759,1851,1931,2037,2142,2240,2348,2454,2562,2735,9049", "endColumns": "119,102,115,85,104,118,79,77,91,92,94,93,95,93,95,94,91,91,79,105,104,97,107,105,107,172,99,80", "endOffsets": "220,323,439,525,630,749,829,907,999,1092,1187,1281,1377,1471,1567,1662,1754,1846,1926,2032,2137,2235,2343,2449,2557,2730,2830,9125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,279,394,507,584,677,788,907,1018,1159,1239,1346,1435,1528,1638,1757,1863,2006,2148,2282,2455,2583,2704,2830,2949,3039,3133,3255,3384,3479,3589,3694,3839,3988,4092,4187,4268,4346,4428,4511,4607,4690,4773,4869,4971,5063,5157,5242,5346,5438,5533,5675,5761,5876", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "163,274,389,502,579,672,783,902,1013,1154,1234,1341,1430,1523,1633,1752,1858,2001,2143,2277,2450,2578,2699,2825,2944,3034,3128,3250,3379,3474,3584,3689,3834,3983,4087,4182,4263,4341,4423,4506,4602,4685,4768,4864,4966,5058,5152,5237,5341,5433,5528,5670,5756,5871,5963"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2835,2948,3059,3174,4195,4272,4365,4476,4595,4706,4847,4927,5034,5123,5216,5326,5445,5551,5694,5836,5970,6143,6271,6392,6518,6637,6727,6821,6943,7072,7167,7277,7382,7527,7676,7780,8072,8239,8967,9202,9386,9760,9843,9926,10022,10124,10216,10310,10395,10499,10591,10686,10828,10914,11029", "endColumns": "112,110,114,112,76,92,110,118,110,140,79,106,88,92,109,118,105,142,141,133,172,127,120,125,118,89,93,121,128,94,109,104,144,148,103,94,80,77,81,82,95,82,82,95,101,91,93,84,103,91,94,141,85,114,91", "endOffsets": "2943,3054,3169,3282,4267,4360,4471,4590,4701,4842,4922,5029,5118,5211,5321,5440,5546,5689,5831,5965,6138,6266,6387,6513,6632,6722,6816,6938,7067,7162,7272,7377,7522,7671,7775,7870,8148,8312,9044,9280,9477,9838,9921,10017,10119,10211,10305,10390,10494,10586,10681,10823,10909,11024,11116"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4018,4111,7875,7969,8153,8317,8397,8486,8574,8656,8727,8797,8880,9130,9482,9567,9637", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "4106,4190,7964,8067,8234,8392,8481,8569,8651,8722,8792,8875,8962,9197,9562,9632,9755"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,456,562,665,786", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "148,250,350,451,557,660,781,882"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3287,3385,3487,3587,3688,3794,3897,9285", "endColumns": "97,101,99,100,105,102,120,100", "endOffsets": "3380,3482,3582,3683,3789,3892,4013,9381"}}]}]}