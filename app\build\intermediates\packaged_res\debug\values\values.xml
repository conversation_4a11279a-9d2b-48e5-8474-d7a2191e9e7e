<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="black">#FF000000</color>
    <color name="colorOnPrimary">#FFFFFF</color>
    <color name="colorOnSecondary">#000000</color>
    <color name="colorPrimary">#6200EE</color>
    <color name="colorPrimaryVariant">#3700B3</color>
    <color name="colorSecondary">#03DAC5</color>
    <color name="colorSecondaryVariant">#018786</color>
    <color name="white">#FFFFFFFF</color>
    <string name="app_name">心率监测器</string>
    <style name="Theme.HeartRateMonitor" parent="Theme.MaterialComponents.DayNight.NoActionBar">
        
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryVariant">@color/colorPrimaryVariant</item>
        <item name="colorOnPrimary">@color/colorOnPrimary</item>
        
        <item name="colorSecondary">@color/colorSecondary</item>
        <item name="colorSecondaryVariant">@color/colorSecondaryVariant</item>
        <item name="colorOnSecondary">@color/colorOnSecondary</item>
        
        <item name="android:statusBarColor">?attr/colorPrimaryVariant</item>
    </style>
</resources>