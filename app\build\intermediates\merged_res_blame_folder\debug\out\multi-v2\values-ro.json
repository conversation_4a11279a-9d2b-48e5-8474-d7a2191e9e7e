{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\a42b12ad0a51f8e308439dbb8e147289\\transformed\\material3-1.1.2\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,181,304,417,539,616,709,820,953,1069,1208,1288,1383,1474,1568,1684,1807,1907,2040,2171,2308,2480,2613,2728,2848,2971,3063,3155,3278,3415,3511,3612,3719,3854,3996,4104,4203,4275,4349,4431,4515,4612,4690,4769,4864,4964,5055,5155,5238,5345,5441,5550,5671,5749,5860", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "176,299,412,534,611,704,815,948,1064,1203,1283,1378,1469,1563,1679,1802,1902,2035,2166,2303,2475,2608,2723,2843,2966,3058,3150,3273,3410,3506,3607,3714,3849,3991,4099,4198,4270,4344,4426,4510,4607,4685,4764,4859,4959,5050,5150,5233,5340,5436,5545,5666,5744,5855,5955"}, "to": {"startLines": "34,35,36,37,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,89,92,160,163,165,169,170,171,172,173,174,175,176,177,178,179,180,181,182", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3117,3243,3366,3479,5268,5345,5438,5549,5682,5798,5937,6017,6112,6203,6297,6413,6536,6636,6769,6900,7037,7209,7342,7457,7577,7700,7792,7884,8007,8144,8240,8341,8448,8583,8725,8833,9131,9357,14861,15100,15285,15641,15719,15798,15893,15993,16084,16184,16267,16374,16470,16579,16700,16778,16889", "endColumns": "125,122,112,121,76,92,110,132,115,138,79,94,90,93,115,122,99,132,130,136,171,132,114,119,122,91,91,122,136,95,100,106,134,141,107,98,71,73,81,83,96,77,78,94,99,90,99,82,106,95,108,120,77,110,99", "endOffsets": "3238,3361,3474,3596,5340,5433,5544,5677,5793,5932,6012,6107,6198,6292,6408,6531,6631,6764,6895,7032,7204,7337,7452,7572,7695,7787,7879,8002,8139,8235,8336,8443,8578,8720,8828,8927,9198,9426,14938,15179,15377,15714,15793,15888,15988,16079,16179,16262,16369,16465,16574,16695,16773,16884,16984"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\d309ccf62c6e6afb4fd21dd867c4c64e\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,983,1052,1140,1230,1303,1380,1447", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,978,1047,1135,1225,1298,1375,1442,1557"}, "to": {"startLines": "53,54,87,88,90,95,96,153,154,155,156,158,159,162,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5087,5184,8932,9029,9203,9600,9678,14290,14381,14463,14535,14683,14771,15027,15382,15459,15526", "endColumns": "96,83,96,101,87,77,86,90,81,71,68,87,89,72,76,66,114", "endOffsets": "5179,5263,9024,9126,9286,9673,9760,14376,14458,14530,14599,14766,14856,15095,15454,15521,15636"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\184958b59b0b0fbc0694ac3308e227ed\\transformed\\material-1.11.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,321,413,501,588,684,774,875,996,1080,1146,1241,1315,1375,1459,1521,1587,1645,1718,1781,1837,1956,2013,2074,2130,2204,2349,2435,2519,2652,2734,2817,2963,3053,3133,3188,3239,3305,3378,3456,3544,3629,3700,3777,3851,3923,4029,4120,4194,4289,4387,4461,4541,4642,4695,4781,4847,4936,5026,5088,5152,5215,5289,5401,5511,5621,5726,5785,5840", "endLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "316,408,496,583,679,769,870,991,1075,1141,1236,1310,1370,1454,1516,1582,1640,1713,1776,1832,1951,2008,2069,2125,2199,2344,2430,2514,2647,2729,2812,2958,3048,3128,3183,3234,3300,3373,3451,3539,3624,3695,3772,3846,3918,4024,4115,4189,4284,4382,4456,4536,4637,4690,4776,4842,4931,5021,5083,5147,5210,5284,5396,5506,5616,5721,5780,5835,5914"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,91,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,157", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3601,3693,3781,3868,3964,4781,4882,5003,9291,9431,9526,9765,9825,9909,9971,10037,10095,10168,10231,10287,10406,10463,10524,10580,10654,10799,10885,10969,11102,11184,11267,11413,11503,11583,11638,11689,11755,11828,11906,11994,12079,12150,12227,12301,12373,12479,12570,12644,12739,12837,12911,12991,13092,13145,13231,13297,13386,13476,13538,13602,13665,13739,13851,13961,14071,14176,14235,14604", "endLines": "6,38,39,40,41,42,50,51,52,91,93,94,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,157", "endColumns": "12,91,87,86,95,89,100,120,83,65,94,73,59,83,61,65,57,72,62,55,118,56,60,55,73,144,85,83,132,81,82,145,89,79,54,50,65,72,77,87,84,70,76,73,71,105,90,73,94,97,73,79,100,52,85,65,88,89,61,63,62,73,111,109,109,104,58,54,78", "endOffsets": "366,3688,3776,3863,3959,4049,4877,4998,5082,9352,9521,9595,9820,9904,9966,10032,10090,10163,10226,10282,10401,10458,10519,10575,10649,10794,10880,10964,11097,11179,11262,11408,11498,11578,11633,11684,11750,11823,11901,11989,12074,12145,12222,12296,12368,12474,12565,12639,12734,12832,12906,12986,13087,13140,13226,13292,13381,13471,13533,13597,13660,13734,13846,13956,14066,14171,14230,14285,14678"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\af226701e07b6e5753175ffad1df9a85\\transformed\\appcompat-1.6.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,917,1008,1101,1196,1290,1390,1483,1578,1672,1763,1855,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "221,325,438,522,626,747,832,912,1003,1096,1191,1285,1385,1478,1573,1667,1758,1850,1933,2045,2153,2253,2367,2473,2579,2743,2846,2930"}, "to": {"startLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,161", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "371,492,596,709,793,897,1018,1103,1183,1274,1367,1462,1556,1656,1749,1844,1938,2029,2121,2204,2316,2424,2524,2638,2744,2850,3014,14943", "endColumns": "120,103,112,83,103,120,84,79,90,92,94,93,99,92,94,93,90,91,82,111,107,99,113,105,105,163,102,83", "endOffsets": "487,591,704,788,892,1013,1098,1178,1269,1362,1457,1551,1651,1744,1839,1933,2024,2116,2199,2311,2419,2519,2633,2739,2845,3009,3112,15022"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\2ccd268e0ca2e1cd10ec650ac8067a3e\\transformed\\core-1.12.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "43,44,45,46,47,48,49,164", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "4054,4152,4254,4354,4453,4555,4664,15184", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "4147,4249,4349,4448,4550,4659,4776,15280"}}]}]}