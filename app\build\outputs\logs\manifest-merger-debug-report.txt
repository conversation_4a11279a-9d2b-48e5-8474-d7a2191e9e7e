-- Merging decision tree log ---
manifest
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:2:1-38:12
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:2:1-38:12
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\184958b59b0b0fbc0694ac3308e227ed\transformed\material-1.11.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1877d7f4568cf535be94336201cf41bd\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e5b5e14697721178082f0bc660e63a39\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a10e56adaebd955ff36c1d2875bbeb49\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a42b12ad0a51f8e308439dbb8e147289\transformed\material3-1.1.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e58140eaa4ae8298d41af3e70ac6934b\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6efd70bea078a54126003b448682576a\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\61900cb96ffc2bd496f96a7279063865\transformed\constraintlayout-2.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af7216361ca839e24eaca4905b35b800\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af226701e07b6e5753175ffad1df9a85\transformed\appcompat-1.6.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f07c45ccc9c9f6765efdcfd15aa9d79\transformed\viewpager2-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\682f8c367c2a77ce64b8ceb9c7d65505\transformed\fragment-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.compose.material:material:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cb039d1fd61fe752c0062472ed64bac\transformed\material-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\81df51f702dff8c0f04a9b83686c6307\transformed\material-icons-core-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ed94e7311c11afa00582f341d653c8d\transformed\material-ripple-1.4.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9233e743b82e0cb484b3b4a59c06a4df\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\264341cdf2e625e94c10e6b72937b80f\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1fdb11b6ae11c88aa0c89de1f6a5e3d7\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a1114135f4fa6fb88bb01f57a8a9eea\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a29acec9d9ce4800fd7a3e2eebb17d5\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4630228c862ed1957d55436ed6d57533\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\56e2e357cf81bf4be5b84fc47197f265\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a10b3c36e1c6f8a50824e2bb78d71\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d337d0e51b02264548bfa59aa5cc68\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\05dee1ddf815ba13fa79508d5d8b20fc\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4cd2072561e27e133d49e442e4e4e033\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5536ac2067c4b2d1d542c9572100b921\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4787f17c81d8154c86edd474b157d98\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bb60db1eb68c7e49fe971ae254b3a60\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bce7d068920674cd1146d66730e92c75\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\683c953a3447f274c38464c2eb0f2e9e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60a203ef5398fafe3c1e8b7f075b0420\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\66e756ebba14b40e4561db7c19a1e7af\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1011bfb828d1f3aada01723c1dde77fd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\89e2d333b353016eb917f0384b488f36\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcc249c3ba3ff1750edcdd1fec45f44\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d31fd368cf97a6796f8d7e12b734441\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a93036d45998da47bd8f352baecdcb1e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6802db8b0738a1f74dc288d6675e755c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cf1f83319a1dee0c16cd111eb7e4616\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ead64fe25185be763bb4647255bbf39\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d309ccf62c6e6afb4fd21dd867c4c64e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c309f36f00b2fdc2e76197abe10e7d3f\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd59a48df0d88a42976f9c034d9002b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2cfb57adc2fe163df1c83b78efb6f416\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2224d3b0c151c68b338e81807c7dd4\transformed\transition-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\97b7c4db7d57f9862eac14712ffdb75d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fda8d97d58f9c4911a77a3233bde6bbf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a57ddfbdb1d17761e4124ec62f05f10\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\228af82e2251dd57db0a21a49098915b\transformed\recyclerview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ec4b573f4cd7e8c5b5b7d1ef665b2df\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0577a48dd9a7730378814b706baef75\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c396b7ed1ceb896856f82c1e69227e93\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0f6a21bbc1725885c6fb4d4ab876c33\transformed\core-ktx-1.12.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\663a56a4db9c4bf3d7c9c2aa85fc9fc9\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\818d2c39f3892eeefd3ff25f1531eb56\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eadd566cfce3f74c25d9758226536a77\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd8f6781ddc94fff899061ce8f8de31\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:2:1-20:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f47d16a6556e4855c9392813a19b75da\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\6ae84c99dd4f3860f7ee1ac1db063087\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e96b204fe1df1541c103ca139cecd1e\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\09a2cd8c134e62d6cf73fc6e22c05804\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e0d1de627058a0ecd200583a07c48b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12eaebcf2fa31f7d4d4c3b96f9aa98aa\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e21e013573ba165400a6887dc3f5c897\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61674fa44124cb0d0250eb6f3c5ae24\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c233a84d844a77a5db64b4bba6b81187\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\954307fe9eb1384af816411d226c6881\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.CAMERA
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:5-65
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:22-62
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:5-81
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:5-80
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:22-77
uses-feature#android.hardware.camera
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:9:5-11:35
	android:required
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:11:9-32
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:10:9-47
uses-feature#android.hardware.camera.flash
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:12:5-14:35
	android:required
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:14:9-32
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:13:9-53
application
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:16:5-36:19
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:16:5-36:19
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\184958b59b0b0fbc0694ac3308e227ed\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\184958b59b0b0fbc0694ac3308e227ed\transformed\material-1.11.0\AndroidManifest.xml:22:5-20
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\61900cb96ffc2bd496f96a7279063865\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\61900cb96ffc2bd496f96a7279063865\transformed\constraintlayout-2.0.1\AndroidManifest.xml:9:5-20
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\09a2cd8c134e62d6cf73fc6e22c05804\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\09a2cd8c134e62d6cf73fc6e22c05804\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:23:9-35
	android:label
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:21:9-41
	android:fullBackupContent
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:19:9-54
	android:roundIcon
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:22:9-54
	tools:targetApi
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:25:9-29
	android:icon
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:20:9-43
	android:allowBackup
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:17:9-35
	android:theme
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:24:9-54
	android:dataExtractionRules
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:18:9-65
activity#com.heartrate.monitor.MainActivity
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:26:9-35:20
	android:label
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:29:13-45
	android:exported
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:28:13-36
	android:theme
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:30:13-58
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:27:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:31:13-34:29
action#android.intent.action.MAIN
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:17-69
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:25-66
category#android.intent.category.LAUNCHER
ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:17-77
	android:name
		ADDED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:27-74
uses-sdk
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\184958b59b0b0fbc0694ac3308e227ed\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.11.0] C:\Users\<USER>\.gradle\caches\transforms-3\184958b59b0b0fbc0694ac3308e227ed\transformed\material-1.11.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1877d7f4568cf535be94336201cf41bd\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\1877d7f4568cf535be94336201cf41bd\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e5b5e14697721178082f0bc660e63a39\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\e5b5e14697721178082f0bc660e63a39\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a10e56adaebd955ff36c1d2875bbeb49\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a10e56adaebd955ff36c1d2875bbeb49\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a42b12ad0a51f8e308439dbb8e147289\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.1.2] C:\Users\<USER>\.gradle\caches\transforms-3\a42b12ad0a51f8e308439dbb8e147289\transformed\material3-1.1.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e58140eaa4ae8298d41af3e70ac6934b\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\e58140eaa4ae8298d41af3e70ac6934b\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6efd70bea078a54126003b448682576a\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\6efd70bea078a54126003b448682576a\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\61900cb96ffc2bd496f96a7279063865\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-3\61900cb96ffc2bd496f96a7279063865\transformed\constraintlayout-2.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af7216361ca839e24eaca4905b35b800\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af7216361ca839e24eaca4905b35b800\transformed\appcompat-resources-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af226701e07b6e5753175ffad1df9a85\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.6.1] C:\Users\<USER>\.gradle\caches\transforms-3\af226701e07b6e5753175ffad1df9a85\transformed\appcompat-1.6.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f07c45ccc9c9f6765efdcfd15aa9d79\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager2:viewpager2:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\9f07c45ccc9c9f6765efdcfd15aa9d79\transformed\viewpager2-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\682f8c367c2a77ce64b8ceb9c7d65505\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-3\682f8c367c2a77ce64b8ceb9c7d65505\transformed\fragment-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.compose.material:material:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cb039d1fd61fe752c0062472ed64bac\transformed\material-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\1cb039d1fd61fe752c0062472ed64bac\transformed\material-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\81df51f702dff8c0f04a9b83686c6307\transformed\material-icons-core-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\81df51f702dff8c0f04a9b83686c6307\transformed\material-icons-core-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ed94e7311c11afa00582f341d653c8d\transformed\material-ripple-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.1] C:\Users\<USER>\.gradle\caches\transforms-3\9ed94e7311c11afa00582f341d653c8d\transformed\material-ripple-1.4.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9233e743b82e0cb484b3b4a59c06a4df\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\9233e743b82e0cb484b3b4a59c06a4df\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\264341cdf2e625e94c10e6b72937b80f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\264341cdf2e625e94c10e6b72937b80f\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1fdb11b6ae11c88aa0c89de1f6a5e3d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\1fdb11b6ae11c88aa0c89de1f6a5e3d7\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a1114135f4fa6fb88bb01f57a8a9eea\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a1114135f4fa6fb88bb01f57a8a9eea\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a29acec9d9ce4800fd7a3e2eebb17d5\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0a29acec9d9ce4800fd7a3e2eebb17d5\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4630228c862ed1957d55436ed6d57533\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4630228c862ed1957d55436ed6d57533\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\56e2e357cf81bf4be5b84fc47197f265\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\56e2e357cf81bf4be5b84fc47197f265\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a10b3c36e1c6f8a50824e2bb78d71\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\fd0a10b3c36e1c6f8a50824e2bb78d71\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d337d0e51b02264548bfa59aa5cc68\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\03d337d0e51b02264548bfa59aa5cc68\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\05dee1ddf815ba13fa79508d5d8b20fc\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\05dee1ddf815ba13fa79508d5d8b20fc\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4cd2072561e27e133d49e442e4e4e033\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\4cd2072561e27e133d49e442e4e4e033\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5536ac2067c4b2d1d542c9572100b921\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\5536ac2067c4b2d1d542c9572100b921\transformed\emoji2-views-helper-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4787f17c81d8154c86edd474b157d98\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\e4787f17c81d8154c86edd474b157d98\transformed\lifecycle-livedata-core-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bb60db1eb68c7e49fe971ae254b3a60\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\1bb60db1eb68c7e49fe971ae254b3a60\transformed\lifecycle-livedata-core-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bce7d068920674cd1146d66730e92c75\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\bce7d068920674cd1146d66730e92c75\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\683c953a3447f274c38464c2eb0f2e9e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\683c953a3447f274c38464c2eb0f2e9e\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60a203ef5398fafe3c1e8b7f075b0420\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\60a203ef5398fafe3c1e8b7f075b0420\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\66e756ebba14b40e4561db7c19a1e7af\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\66e756ebba14b40e4561db7c19a1e7af\transformed\lifecycle-livedata-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1011bfb828d1f3aada01723c1dde77fd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\1011bfb828d1f3aada01723c1dde77fd\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\89e2d333b353016eb917f0384b488f36\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-3\89e2d333b353016eb917f0384b488f36\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcc249c3ba3ff1750edcdd1fec45f44\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\ebcc249c3ba3ff1750edcdd1fec45f44\transformed\lifecycle-viewmodel-savedstate-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d31fd368cf97a6796f8d7e12b734441\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\5d31fd368cf97a6796f8d7e12b734441\transformed\lifecycle-viewmodel-ktx-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a93036d45998da47bd8f352baecdcb1e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\a93036d45998da47bd8f352baecdcb1e\transformed\lifecycle-viewmodel-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6802db8b0738a1f74dc288d6675e755c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\6802db8b0738a1f74dc288d6675e755c\transformed\lifecycle-runtime-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cf1f83319a1dee0c16cd111eb7e4616\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\9cf1f83319a1dee0c16cd111eb7e4616\transformed\lifecycle-runtime-ktx-2.7.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ead64fe25185be763bb4647255bbf39\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ead64fe25185be763bb4647255bbf39\transformed\lifecycle-viewmodel-compose-2.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d309ccf62c6e6afb4fd21dd867c4c64e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\d309ccf62c6e6afb4fd21dd867c4c64e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c309f36f00b2fdc2e76197abe10e7d3f\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\transforms-3\c309f36f00b2fdc2e76197abe10e7d3f\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd59a48df0d88a42976f9c034d9002b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fdd59a48df0d88a42976f9c034d9002b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2cfb57adc2fe163df1c83b78efb6f416\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\2cfb57adc2fe163df1c83b78efb6f416\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2224d3b0c151c68b338e81807c7dd4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\dc2224d3b0c151c68b338e81807c7dd4\transformed\transition-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\97b7c4db7d57f9862eac14712ffdb75d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\97b7c4db7d57f9862eac14712ffdb75d\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fda8d97d58f9c4911a77a3233bde6bbf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\fda8d97d58f9c4911a77a3233bde6bbf\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a57ddfbdb1d17761e4124ec62f05f10\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\2a57ddfbdb1d17761e4124ec62f05f10\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\228af82e2251dd57db0a21a49098915b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.recyclerview:recyclerview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\228af82e2251dd57db0a21a49098915b\transformed\recyclerview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ec4b573f4cd7e8c5b5b7d1ef665b2df\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\8ec4b573f4cd7e8c5b5b7d1ef665b2df\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0577a48dd9a7730378814b706baef75\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\f0577a48dd9a7730378814b706baef75\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c396b7ed1ceb896856f82c1e69227e93\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c396b7ed1ceb896856f82c1e69227e93\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0f6a21bbc1725885c6fb4d4ab876c33\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\e0f6a21bbc1725885c6fb4d4ab876c33\transformed\core-ktx-1.12.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\663a56a4db9c4bf3d7c9c2aa85fc9fc9\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\663a56a4db9c4bf3d7c9c2aa85fc9fc9\transformed\annotation-experimental-1.3.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\818d2c39f3892eeefd3ff25f1531eb56\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\818d2c39f3892eeefd3ff25f1531eb56\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eadd566cfce3f74c25d9758226536a77\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\eadd566cfce3f74c25d9758226536a77\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd8f6781ddc94fff899061ce8f8de31\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [com.github.PhilJay:MPAndroidChart:v3.1.0] C:\Users\<USER>\.gradle\caches\transforms-3\7cd8f6781ddc94fff899061ce8f8de31\transformed\MPAndroidChart-v3.1.0\AndroidManifest.xml:16:5-18:41
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f47d16a6556e4855c9392813a19b75da\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\f47d16a6556e4855c9392813a19b75da\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\6ae84c99dd4f3860f7ee1ac1db063087\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-3\6ae84c99dd4f3860f7ee1ac1db063087\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e96b204fe1df1541c103ca139cecd1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\1e96b204fe1df1541c103ca139cecd1e\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\09a2cd8c134e62d6cf73fc6e22c05804\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\09a2cd8c134e62d6cf73fc6e22c05804\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e0d1de627058a0ecd200583a07c48b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-3\8e0d1de627058a0ecd200583a07c48b2\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12eaebcf2fa31f7d4d4c3b96f9aa98aa\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\12eaebcf2fa31f7d4d4c3b96f9aa98aa\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e21e013573ba165400a6887dc3f5c897\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\e21e013573ba165400a6887dc3f5c897\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61674fa44124cb0d0250eb6f3c5ae24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\a61674fa44124cb0d0250eb6f3c5ae24\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c233a84d844a77a5db64b4bba6b81187\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\c233a84d844a77a5db64b4bba6b81187\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\954307fe9eb1384af816411d226c6881\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-3\954307fe9eb1384af816411d226c6881\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\a58aedc654e6a4cdd9f1321edd6dd011\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-3\9f32a872f0b1731ac06773ebeb3ffa7e\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
permission#com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
uses-permission#com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
