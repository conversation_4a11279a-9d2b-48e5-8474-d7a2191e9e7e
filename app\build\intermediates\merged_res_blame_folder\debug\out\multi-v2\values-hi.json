{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-62:/values-hi/values-hi.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\ff3e7a6cbd6877b655429719bd7e51c8\\transformed\\appcompat-1.6.1\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,211,309,419,505,607,728,806,883,974,1067,1162,1256,1356,1449,1544,1638,1729,1820,1901,2006,2108,2206,2316,2419,2528,2686,2787", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "206,304,414,500,602,723,801,878,969,1062,1157,1251,1351,1444,1539,1633,1724,1815,1896,2001,2103,2201,2311,2414,2523,2681,2782,2864"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,160", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "309,415,513,623,709,811,932,1010,1087,1178,1271,1366,1460,1560,1653,1748,1842,1933,2024,2105,2210,2312,2410,2520,2623,2732,2890,14733", "endColumns": "105,97,109,85,101,120,77,76,90,92,94,93,99,92,94,93,90,90,80,104,101,97,109,102,108,157,100,81", "endOffsets": "410,508,618,704,806,927,1005,1082,1173,1266,1361,1455,1555,1648,1743,1837,1928,2019,2100,2205,2307,2405,2515,2618,2727,2885,2986,14810"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,361,462,575,681,808", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "148,251,356,457,570,676,803,904"}, "to": {"startLines": "42,43,44,45,46,47,48,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3889,3987,4090,4195,4296,4409,4515,14974", "endColumns": "97,102,104,100,112,105,126,100", "endOffsets": "3982,4085,4190,4291,4404,4510,4637,15070"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,283,376,474,563,641,738,827,912,980,1049,1130,1215,1288,1369,1435", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "195,278,371,469,558,636,733,822,907,975,1044,1125,1210,1283,1364,1430,1550"}, "to": {"startLines": "52,53,86,87,89,94,95,152,153,154,155,157,158,161,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4964,5059,8794,8887,9057,9455,9533,14095,14184,14269,14337,14485,14566,14815,15176,15257,15323", "endColumns": "94,82,92,97,88,77,96,88,84,67,68,80,84,72,80,65,119", "endOffsets": "5054,5137,8882,8980,9141,9528,9625,14179,14264,14332,14401,14561,14646,14883,15252,15318,15438"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\fef0b4911f7fba22eaa6d4a2664b7d03\\transformed\\material-1.11.0\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,259,337,413,494,601,697,804,936,1019,1084,1178,1247,1306,1391,1454,1517,1575,1640,1701,1762,1868,1926,1986,2045,2115,2231,2310,2390,2524,2599,2675,2812,2909,3007,3064,3119,3185,3255,3332,3418,3503,3571,3647,3728,3806,3907,3993,4080,4177,4276,4350,4420,4524,4578,4665,4732,4822,4914,4976,5040,5103,5169,5274,5384,5485,5592,5653,5712", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "254,332,408,489,596,692,799,931,1014,1079,1173,1242,1301,1386,1449,1512,1570,1635,1696,1757,1863,1921,1981,2040,2110,2226,2305,2385,2519,2594,2670,2807,2904,3002,3059,3114,3180,3250,3327,3413,3498,3566,3642,3723,3801,3902,3988,4075,4172,4271,4345,4415,4519,4573,4660,4727,4817,4909,4971,5035,5098,5164,5269,5379,5480,5587,5648,5707,5786"}, "to": {"startLines": "2,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3451,3529,3605,3686,3793,4642,4749,4881,9146,9292,9386,9630,9689,9774,9837,9900,9958,10023,10084,10145,10251,10309,10369,10428,10498,10614,10693,10773,10907,10982,11058,11195,11292,11390,11447,11502,11568,11638,11715,11801,11886,11954,12030,12111,12189,12290,12376,12463,12560,12659,12733,12803,12907,12961,13048,13115,13205,13297,13359,13423,13486,13552,13657,13767,13868,13975,14036,14406", "endLines": "5,37,38,39,40,41,49,50,51,90,92,93,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,156", "endColumns": "12,77,75,80,106,95,106,131,82,64,93,68,58,84,62,62,57,64,60,60,105,57,59,58,69,115,78,79,133,74,75,136,96,97,56,54,65,69,76,85,84,67,75,80,77,100,85,86,96,98,73,69,103,53,86,66,89,91,61,63,62,65,104,109,100,106,60,58,78", "endOffsets": "304,3524,3600,3681,3788,3884,4744,4876,4959,9206,9381,9450,9684,9769,9832,9895,9953,10018,10079,10140,10246,10304,10364,10423,10493,10609,10688,10768,10902,10977,11053,11190,11287,11385,11442,11497,11563,11633,11710,11796,11881,11949,12025,12106,12184,12285,12371,12458,12555,12654,12728,12798,12902,12956,13043,13110,13200,13292,13354,13418,13481,13547,13652,13762,13863,13970,14031,14090,14480"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-hi\\values-hi.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,283,403,515,597,689,799,933,1049,1199,1280,1379,1466,1559,1680,1796,1900,2041,2183,2309,2476,2599,2709,2824,2947,3035,3126,3250,3372,3467,3565,3673,3814,3962,4072,4167,4239,4320,4402,4488,4589,4665,4745,4841,4937,5028,5126,5208,5306,5400,5499,5610,5686,5782", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "164,278,398,510,592,684,794,928,1044,1194,1275,1374,1461,1554,1675,1791,1895,2036,2178,2304,2471,2594,2704,2819,2942,3030,3121,3245,3367,3462,3560,3668,3809,3957,4067,4162,4234,4315,4397,4483,4584,4660,4740,4836,4932,5023,5121,5203,5301,5395,5494,5605,5681,5777,5867"}, "to": {"startLines": "33,34,35,36,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,88,91,159,162,164,168,169,170,171,172,173,174,175,176,177,178,179,180,181", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2991,3105,3219,3339,5142,5224,5316,5426,5560,5676,5826,5907,6006,6093,6186,6307,6423,6527,6668,6810,6936,7103,7226,7336,7451,7574,7662,7753,7877,7999,8094,8192,8300,8441,8589,8699,8985,9211,14651,14888,15075,15443,15519,15599,15695,15791,15882,15980,16062,16160,16254,16353,16464,16540,16636", "endColumns": "113,113,119,111,81,91,109,133,115,149,80,98,86,92,120,115,103,140,141,125,166,122,109,114,122,87,90,123,121,94,97,107,140,147,109,94,71,80,81,85,100,75,79,95,95,90,97,81,97,93,98,110,75,95,89", "endOffsets": "3100,3214,3334,3446,5219,5311,5421,5555,5671,5821,5902,6001,6088,6181,6302,6418,6522,6663,6805,6931,7098,7221,7331,7446,7569,7657,7748,7872,7994,8089,8187,8295,8436,8584,8694,8789,9052,9287,14728,14969,15171,15514,15594,15690,15786,15877,15975,16057,16155,16249,16348,16459,16535,16631,16721"}}]}]}