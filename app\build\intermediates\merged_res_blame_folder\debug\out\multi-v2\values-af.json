{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-af/values-af.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,451,558,667,787", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "148,250,348,446,553,662,782,883"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3227,3325,3427,3525,3623,3730,3839,9115", "endColumns": "97,101,97,97,106,108,119,100", "endOffsets": "3320,3422,3520,3618,3725,3834,3954,9211"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,168,282,393,505,581,681,799,936,1060,1210,1291,1386,1472,1571,1682,1799,1899,2023,2144,2272,2434,2555,2673,2793,2919,3006,3101,3213,3335,3431,3536,3635,3767,3903,4005,4098,4171,4247,4328,4412,4513,4590,4669,4764,4858,4949,5043,5127,5226,5322,5420,5532,5609,5705", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "163,277,388,500,576,676,794,931,1055,1205,1286,1381,1467,1566,1677,1794,1894,2018,2139,2267,2429,2550,2668,2788,2914,3001,3096,3208,3330,3426,3531,3630,3762,3898,4000,4093,4166,4242,4323,4407,4508,4585,4664,4759,4853,4944,5038,5122,5221,5317,5415,5527,5604,5700,5792"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2777,2890,3004,3115,4142,4218,4318,4436,4573,4697,4847,4928,5023,5109,5208,5319,5436,5536,5660,5781,5909,6071,6192,6310,6430,6556,6643,6738,6850,6972,7068,7173,7272,7404,7540,7642,7933,8092,8799,9031,9216,9588,9665,9744,9839,9933,10024,10118,10202,10301,10397,10495,10607,10684,10780", "endColumns": "112,113,110,111,75,99,117,136,123,149,80,94,85,98,110,116,99,123,120,127,161,120,117,119,125,86,94,111,121,95,104,98,131,135,101,92,72,75,80,83,100,76,78,94,93,90,93,83,98,95,97,111,76,95,91", "endOffsets": "2885,2999,3110,3222,4213,4313,4431,4568,4692,4842,4923,5018,5104,5203,5314,5431,5531,5655,5776,5904,6066,6187,6305,6425,6551,6638,6733,6845,6967,7063,7168,7267,7399,7535,7637,7730,8001,8163,8875,9110,9312,9660,9739,9834,9928,10019,10113,10197,10296,10392,10490,10602,10679,10775,10867"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,201,288,385,486,572,648,739,829,915,979,1044,1122,1203,1274,1355,1425", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "196,283,380,481,567,643,734,824,910,974,1039,1117,1198,1269,1350,1420,1540"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3959,4055,7735,7832,8006,8168,8244,8335,8425,8511,8575,8640,8718,8960,9317,9398,9468", "endColumns": "95,86,96,100,85,75,90,89,85,63,64,77,80,70,80,69,119", "endOffsets": "4050,4137,7827,7928,8087,8239,8330,8420,8506,8570,8635,8713,8794,9026,9393,9463,9583"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-af\\values-af.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,875,966,1058,1153,1247,1347,1440,1535,1634,1729,1823,1903,2010,2115,2212,2320,2423,2525,2679,2777", "endColumns": "107,95,105,84,102,117,76,76,90,91,94,93,99,92,94,98,94,93,79,106,104,96,107,102,101,153,97,79", "endOffsets": "208,304,410,495,598,716,793,870,961,1053,1148,1242,1342,1435,1530,1629,1724,1818,1898,2005,2110,2207,2315,2418,2520,2674,2772,2852"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,213,309,415,500,603,721,798,875,966,1058,1153,1247,1347,1440,1535,1634,1729,1823,1903,2010,2115,2212,2320,2423,2525,2679,8880", "endColumns": "107,95,105,84,102,117,76,76,90,91,94,93,99,92,94,98,94,93,79,106,104,96,107,102,101,153,97,79", "endOffsets": "208,304,410,495,598,716,793,870,961,1053,1148,1242,1342,1435,1530,1629,1724,1818,1898,2005,2110,2207,2315,2418,2520,2674,2772,8955"}}]}]}