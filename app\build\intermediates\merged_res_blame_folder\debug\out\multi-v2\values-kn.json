{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-kn/values-kn.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,295,413,532,614,708,820,973,1099,1247,1329,1431,1528,1634,1746,1875,1982,2117,2248,2377,2561,2681,2795,2913,3037,3135,3228,3346,3480,3582,3687,3789,3923,4064,4167,4271,4343,4425,4508,4593,4700,4776,4856,4952,5056,5152,5249,5332,5441,5539,5639,5756,5832,5938", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "170,290,408,527,609,703,815,968,1094,1242,1324,1426,1523,1629,1741,1870,1977,2112,2243,2372,2556,2676,2790,2908,3032,3130,3223,3341,3475,3577,3682,3784,3918,4059,4162,4266,4338,4420,4503,4588,4695,4771,4851,4947,5051,5147,5244,5327,5436,5534,5634,5751,5827,5933,6026"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2859,2979,3099,3217,4262,4344,4438,4550,4703,4829,4977,5059,5161,5258,5364,5476,5605,5712,5847,5978,6107,6291,6411,6525,6643,6767,6865,6958,7076,7210,7312,7417,7519,7653,7794,7897,8197,8358,9103,9347,9533,9904,9980,10060,10156,10260,10356,10453,10536,10645,10743,10843,10960,11036,11142", "endColumns": "119,119,117,118,81,93,111,152,125,147,81,101,96,105,111,128,106,134,130,128,183,119,113,117,123,97,92,117,133,101,104,101,133,140,102,103,71,81,82,84,106,75,79,95,103,95,96,82,108,97,99,116,75,105,92", "endOffsets": "2974,3094,3212,3331,4339,4433,4545,4698,4824,4972,5054,5156,5253,5359,5471,5600,5707,5842,5973,6102,6286,6406,6520,6638,6762,6860,6953,7071,7205,7307,7412,7514,7648,7789,7892,7996,8264,8435,9181,9427,9635,9975,10055,10151,10255,10351,10448,10531,10640,10738,10838,10955,11031,11137,11230"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,256,357,463,564,672,800", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "148,251,352,458,559,667,795,896"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3336,3434,3537,3638,3744,3845,3953,9432", "endColumns": "97,102,100,105,100,107,127,100", "endOffsets": "3429,3532,3633,3739,3840,3948,4076,9528"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,920,1011,1103,1198,1292,1393,1486,1581,1675,1766,1857,1938,2054,2164,2263,2376,2481,2595,2759,2859", "endColumns": "113,111,112,87,106,125,77,76,90,91,94,93,100,92,94,93,90,90,80,115,109,98,112,104,113,163,99,81", "endOffsets": "214,326,439,527,634,760,838,915,1006,1098,1193,1287,1388,1481,1576,1670,1761,1852,1933,2049,2159,2258,2371,2476,2590,2754,2854,2936"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,331,444,532,639,765,843,920,1011,1103,1198,1292,1393,1486,1581,1675,1766,1857,1938,2054,2164,2263,2376,2481,2595,2759,9186", "endColumns": "113,111,112,87,106,125,77,76,90,91,94,93,100,92,94,93,90,90,80,115,109,98,112,104,113,163,99,81", "endOffsets": "214,326,439,527,634,760,838,915,1006,1098,1193,1287,1388,1481,1576,1670,1761,1852,1933,2049,2159,2258,2371,2476,2590,2754,2854,9263"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-kn\\values-kn.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,382,482,571,655,748,839,924,995,1066,1148,1234,1313,1390,1459", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "197,281,377,477,566,650,743,834,919,990,1061,1143,1229,1308,1385,1454,1572"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4081,4178,8001,8097,8269,8440,8524,8617,8708,8793,8864,8935,9017,9268,9640,9717,9786", "endColumns": "96,83,95,99,88,83,92,90,84,70,70,81,85,78,76,68,117", "endOffsets": "4173,4257,8092,8192,8353,8519,8612,8703,8788,8859,8930,9012,9098,9342,9712,9781,9899"}}]}]}