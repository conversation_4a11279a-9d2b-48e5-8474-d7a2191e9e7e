1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.heartrate.monitor"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:5-65
11-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:22-62
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:5-81
12-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:5-80
13-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:22-77
14
15    <uses-feature
15-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:9:5-11:35
16        android:name="android.hardware.camera"
16-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="true" />
17-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:11:9-32
18    <uses-feature
18-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:12:5-14:35
19        android:name="android.hardware.camera.flash"
19-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:13:9-53
20        android:required="true" />
20-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:14:9-32
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:16:5-36:19
29        android:allowBackup="true"
29-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:17:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\2ccd268e0ca2e1cd10ec650ac8067a3e\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:18:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:19:9-54
35        android:icon="@mipmap/ic_launcher"
35-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:20:9-43
36        android:label="@string/app_name"
36-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:21:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:22:9-54
38        android:supportsRtl="true"
38-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:23:9-35
39        android:theme="@style/Theme.HeartRateMonitor" >
39-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:24:9-54
40        <activity
40-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:26:9-35:20
41            android:name="com.heartrate.monitor.MainActivity"
41-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:27:13-41
42            android:exported="true"
42-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:28:13-36
43            android:label="@string/app_name"
43-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:29:13-45
44            android:theme="@style/Theme.HeartRateMonitor" >
44-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:30:13-58
45            <intent-filter>
45-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:31:13-34:29
46                <action android:name="android.intent.action.MAIN" />
46-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:17-69
46-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:17-77
48-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:27-74
49            </intent-filter>
50        </activity>
51
52        <service
52-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
53            android:name="androidx.camera.core.impl.MetadataHolderService"
53-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
54            android:enabled="false"
54-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
55            android:exported="false" >
55-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
56            <meta-data
56-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
57                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
57-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
58                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
58-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\0e37e08efdc270925b5c40ce7fbb7f9c\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
59        </service>
60
61        <provider
61-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
62            android:name="androidx.startup.InitializationProvider"
62-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
63            android:authorities="com.heartrate.monitor.androidx-startup"
63-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
64            android:exported="false" >
64-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
65            <meta-data
65-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
66                android:name="androidx.emoji2.text.EmojiCompatInitializer"
66-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
67                android:value="androidx.startup" />
67-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\da3398c4e0ab94412c8944ea49b75246\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
68            <meta-data
68-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
69                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
69-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
70                android:value="androidx.startup" />
70-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\de0aab144c5a415a89446dd83fe34e27\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
71            <meta-data
71-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
72-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
73                android:value="androidx.startup" />
73-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
74        </provider>
75
76        <activity
76-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
77            android:name="androidx.compose.ui.tooling.PreviewActivity"
77-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
78            android:exported="true" />
78-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\998fe5af4dd61ac534bc82a3b8e21c03\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
79        <activity
79-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
80            android:name="androidx.activity.ComponentActivity"
80-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
81            android:exported="true" />
81-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\49134ffb815cfa1aa0256497e0d3da3b\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
82
83        <receiver
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
84            android:name="androidx.profileinstaller.ProfileInstallReceiver"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
85            android:directBootAware="false"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
86            android:enabled="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
87            android:exported="true"
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
88            android:permission="android.permission.DUMP" >
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
89            <intent-filter>
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
90                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
90-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
91            </intent-filter>
92            <intent-filter>
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
93                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
93-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
94            </intent-filter>
95            <intent-filter>
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
96                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
96-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
97            </intent-filter>
98            <intent-filter>
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
99                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
99-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\90a5c045bb237d34bfde7528c354fcd3\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
100            </intent-filter>
101        </receiver>
102    </application>
103
104</manifest>
