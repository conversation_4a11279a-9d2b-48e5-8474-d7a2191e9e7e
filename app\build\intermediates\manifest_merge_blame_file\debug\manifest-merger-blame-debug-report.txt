1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.heartrate.monitor"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="34" />
10
11    <uses-permission android:name="android.permission.CAMERA" />
11-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:5-65
11-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:5:22-62
12    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
12-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:5-81
12-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:6:22-78
13    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
13-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:5-80
13-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:7:22-77
14
15    <uses-feature
15-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:9:5-11:35
16        android:name="android.hardware.camera"
16-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:10:9-47
17        android:required="true" />
17-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:11:9-32
18    <uses-feature
18-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:12:5-14:35
19        android:name="android.hardware.camera.flash"
19-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:13:9-53
20        android:required="true" />
20-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:14:9-32
21
22    <permission
22-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:22:5-24:47
23        android:name="com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
23-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:23:9-81
24        android:protectionLevel="signature" />
24-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:24:9-44
25
26    <uses-permission android:name="com.heartrate.monitor.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:26:5-97
26-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:26:22-94
27
28    <application
28-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:16:5-36:19
29        android:allowBackup="true"
29-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:17:9-35
30        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
30-->[androidx.core:core:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-3\4d13162283d09b9c9bedcbf64eea8d38\transformed\core-1.12.0\AndroidManifest.xml:28:18-86
31        android:dataExtractionRules="@xml/data_extraction_rules"
31-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:18:9-65
32        android:debuggable="true"
33        android:extractNativeLibs="false"
34        android:fullBackupContent="@xml/backup_rules"
34-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:19:9-54
35        android:icon="@mipmap/ic_launcher"
35-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:20:9-43
36        android:label="@string/app_name"
36-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:21:9-41
37        android:roundIcon="@mipmap/ic_launcher_round"
37-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:22:9-54
38        android:supportsRtl="true"
38-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:23:9-35
39        android:theme="@style/Theme.HeartRateMonitor" >
39-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:24:9-54
40        <activity
40-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:26:9-35:20
41            android:name="com.heartrate.monitor.MainActivity"
41-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:27:13-41
42            android:exported="true"
42-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:28:13-36
43            android:label="@string/app_name"
43-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:29:13-45
44            android:theme="@style/Theme.HeartRateMonitor" >
44-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:30:13-58
45            <intent-filter>
45-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:31:13-34:29
46                <action android:name="android.intent.action.MAIN" />
46-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:17-69
46-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:32:25-66
47
48                <category android:name="android.intent.category.LAUNCHER" />
48-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:17-77
48-->E:\code\APP\CameraPPG\app\src\main\AndroidManifest.xml:33:27-74
49            </intent-filter>
50        </activity>
51        <activity
51-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0864e2536902f00f08ce486767127571\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:23:9-25:39
52            android:name="androidx.activity.ComponentActivity"
52-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0864e2536902f00f08ce486767127571\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:24:13-63
53            android:exported="true" />
53-->[androidx.compose.ui:ui-test-manifest:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\0864e2536902f00f08ce486767127571\transformed\ui-test-manifest-1.5.4\AndroidManifest.xml:25:13-36
54        <activity
54-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6b275c7f0c0bf46ca92004dbee158335\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
55            android:name="androidx.compose.ui.tooling.PreviewActivity"
55-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6b275c7f0c0bf46ca92004dbee158335\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
56            android:exported="true" />
56-->[androidx.compose.ui:ui-tooling-android:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-3\6b275c7f0c0bf46ca92004dbee158335\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
57
58        <service
58-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
59            android:name="androidx.camera.core.impl.MetadataHolderService"
59-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
60            android:enabled="false"
60-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
61            android:exported="false" >
61-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
62            <meta-data
62-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
63                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
63-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
64                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
64-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-3\05219b8f6ac7da363fd3a99b0c564284\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
65        </service>
66
67        <provider
67-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:24:9-32:20
68            android:name="androidx.startup.InitializationProvider"
68-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:25:13-67
69            android:authorities="com.heartrate.monitor.androidx-startup"
69-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:26:13-68
70            android:exported="false" >
70-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:27:13-37
71            <meta-data
71-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:29:13-31:52
72                android:name="androidx.emoji2.text.EmojiCompatInitializer"
72-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:30:17-75
73                android:value="androidx.startup" />
73-->[androidx.emoji2:emoji2:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-3\ff6c1ae89053807cc379e4ca67326da6\transformed\emoji2-1.4.0\AndroidManifest.xml:31:17-49
74            <meta-data
74-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\81594fa437c329a6bd4a20cb6e64a407\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
75                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
75-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\81594fa437c329a6bd4a20cb6e64a407\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
76                android:value="androidx.startup" />
76-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\transforms-3\81594fa437c329a6bd4a20cb6e64a407\transformed\lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
77            <meta-data
77-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
78                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
78-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
79                android:value="androidx.startup" />
79-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
80        </provider>
81
82        <receiver
82-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
83            android:name="androidx.profileinstaller.ProfileInstallReceiver"
83-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
84            android:directBootAware="false"
84-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
85            android:enabled="true"
85-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
86            android:exported="true"
86-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
87            android:permission="android.permission.DUMP" >
87-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
88            <intent-filter>
88-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
89                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
89-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
90            </intent-filter>
91            <intent-filter>
91-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
92                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
92-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
93            </intent-filter>
94            <intent-filter>
94-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
95                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
95-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
96            </intent-filter>
97            <intent-filter>
97-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
98                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
98-->[androidx.profileinstaller:profileinstaller:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-3\6f6664ef7d9272f9ecff2c30e1249d51\transformed\profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
99            </intent-filter>
100        </receiver>
101    </application>
102
103</manifest>
