{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-es-rUS/values-es-rUS.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,291,410,526,604,698,810,949,1063,1211,1292,1390,1483,1581,1695,1814,1914,2046,2175,2310,2482,2609,2725,2844,2968,3062,3154,3271,3400,3497,3598,3709,3839,3976,4083,4183,4256,4333,4416,4501,4608,4686,4766,4863,4965,5061,5156,5240,5351,5448,5547,5666,5744,5847", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "168,286,405,521,599,693,805,944,1058,1206,1287,1385,1478,1576,1690,1809,1909,2041,2170,2305,2477,2604,2720,2839,2963,3057,3149,3266,3395,3492,3593,3704,3834,3971,4078,4178,4251,4328,4411,4496,4603,4681,4761,4858,4960,5056,5151,5235,5346,5443,5542,5661,5739,5842,5937"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2821,2939,3057,3176,4205,4283,4377,4489,4628,4742,4890,4971,5069,5162,5260,5374,5493,5593,5725,5854,5989,6161,6288,6404,6523,6647,6741,6833,6950,7079,7176,7277,7388,7518,7655,7762,8063,8225,8961,9203,9389,9767,9845,9925,10022,10124,10220,10315,10399,10510,10607,10706,10825,10903,11006", "endColumns": "117,117,118,115,77,93,111,138,113,147,80,97,92,97,113,118,99,131,128,134,171,126,115,118,123,93,91,116,128,96,100,110,129,136,106,99,72,76,82,84,106,77,79,96,101,95,94,83,110,96,98,118,77,102,94", "endOffsets": "2934,3052,3171,3287,4278,4372,4484,4623,4737,4885,4966,5064,5157,5255,5369,5488,5588,5720,5849,5984,6156,6283,6399,6518,6642,6736,6828,6945,7074,7171,7272,7383,7513,7650,7757,7857,8131,8297,9039,9283,9491,9840,9920,10017,10119,10215,10310,10394,10505,10602,10701,10820,10898,11001,11096"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,356,454,561,667,787", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "149,251,351,449,556,662,782,883"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3292,3391,3493,3593,3691,3798,3904,9288", "endColumns": "98,101,99,97,106,105,119,100", "endOffsets": "3386,3488,3588,3686,3793,3899,4019,9384"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,2821", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,2898"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,334,442,527,629,745,830,911,1002,1094,1189,1283,1383,1476,1575,1671,1762,1853,1934,2041,2140,2239,2347,2455,2562,2721,9044", "endColumns": "119,108,107,84,101,115,84,80,90,91,94,93,99,92,98,95,90,90,80,106,98,98,107,107,106,158,99,81", "endOffsets": "220,329,437,522,624,740,825,906,997,1089,1184,1278,1378,1471,1570,1666,1757,1848,1929,2036,2135,2234,2342,2450,2557,2716,2816,9121"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-es-rUS\\values-es-rUS.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,286,384,487,576,655,751,843,930,994,1058,1145,1235,1312,1390,1460", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "199,281,379,482,571,650,746,838,925,989,1053,1140,1230,1307,1385,1455,1578"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4024,4123,7862,7960,8136,8302,8381,8477,8569,8656,8720,8784,8871,9126,9496,9574,9644", "endColumns": "98,81,97,102,88,78,95,91,86,63,63,86,89,76,77,69,122", "endOffsets": "4118,4200,7955,8058,8220,8376,8472,8564,8651,8715,8779,8866,8956,9198,9569,9639,9762"}}]}]}