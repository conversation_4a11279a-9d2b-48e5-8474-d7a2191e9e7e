package com.heartrate.monitor.analysis

import kotlin.math.*

data class AnalysisResult(
    val heartRate: Int,
    val status: String,
    val waveform: List<Float>
)

interface HeartRateAnalysisStrategy {
    fun analyze(data: List<DataPoint>): AnalysisResult
}

data class DataPoint(
    val value: Float,
    val timestamp: Long
)

class HeartRateAnalyzer {
    private val dataPoints = mutableListOf<DataPoint>()
    private var analysisStrategy: HeartRateAnalysisStrategy = FFTAnalysisStrategy()
    
    // 可配置参数
    private val maxDataPoints = 1000
    private val minAnalysisPoints = 100
    
    fun addDataPoint(value: Float, timestamp: Long) {
        dataPoints.add(DataPoint(value, timestamp))
        
        // 保持数据点数量在限制内
        if (dataPoints.size > maxDataPoints) {
            dataPoints.removeAt(0)
        }
    }
    
    fun analyze(): AnalysisResult {
        return if (dataPoints.size >= minAnalysisPoints) {
            analysisStrategy.analyze(dataPoints)
        } else {
            AnalysisResult(0, "数据不足", emptyList())
        }
    }
    
    fun setAnalysisStrategy(strategy: HeartRateAnalysisStrategy) {
        this.analysisStrategy = strategy
    }
    
    fun reset() {
        dataPoints.clear()
    }
}

// FFT分析策略实现
class FFTAnalysisStrategy : HeartRateAnalysisStrategy {
    override fun analyze(data: List<DataPoint>): AnalysisResult {
        // 数据预处理
        val processedData = preprocessData(data)

        // 应用FFT分析
        val heartRate = calculateHeartRateFFT(processedData)

        // 生成波形数据（最近100个点）
        val waveform = data.takeLast(100).map { it.value }

        val status = when {
            heartRate in 60..100 -> "正常"
            heartRate in 50..59 -> "偏低"
            heartRate in 101..120 -> "偏高"
            else -> "异常"
        }

        return AnalysisResult(heartRate, status, waveform)
    }

    private fun preprocessData(data: List<DataPoint>): List<Float> {
        // 移除直流分量
        val mean = data.map { it.value }.average().toFloat()
        val centered = data.map { it.value - mean }

        // 应用低通滤波器
        return applyLowPassFilter(centered)
    }

    private fun applyLowPassFilter(data: List<Float>): List<Float> {
        // 简单的移动平均滤波器
        val windowSize = 5
        return data.windowed(windowSize) { window ->
            window.average().toFloat()
        }
    }

    private fun calculateHeartRateFFT(data: List<Float>): Int {
        // 简化的峰值检测算法
        val peaks = findPeaks(data)

        if (peaks.size < 2) return 0

        // 计算平均间隔
        val intervals = peaks.zipWithNext { a, b -> b - a }
        val avgInterval = intervals.average()

        // 转换为BPM (假设采样率为30fps)
        val samplingRate = 30.0
        val heartRate = (60.0 * samplingRate / avgInterval).roundToInt()

        return heartRate.coerceIn(30, 200)
    }

    private fun findPeaks(data: List<Float>): List<Int> {
        val peaks = mutableListOf<Int>()
        val threshold = data.maxOrNull()?.times(0.6f) ?: 0f

        for (i in 1 until data.size - 1) {
            if (data[i] > data[i - 1] &&
                data[i] > data[i + 1] &&
                data[i] > threshold) {
                peaks.add(i)
            }
        }

        return peaks
    }
}
