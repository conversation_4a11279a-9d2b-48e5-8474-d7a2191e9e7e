package com.heartrate.monitor.viewmodel

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import com.heartrate.monitor.data.HeartRateData
import com.heartrate.monitor.analysis.HeartRateAnalyzer
import com.heartrate.monitor.data.DataExporter

data class HeartRateUiState(
    val heartRate: Int = 0,
    val status: String = "准备中",
    val isRecording: Boolean = false,
    val waveformData: List<Float> = emptyList()
)

class HeartRateViewModel : ViewModel() {
    private val _uiState = MutableStateFlow(HeartRateUiState())
    val uiState: StateFlow<HeartRateUiState> = _uiState.asStateFlow()
    
    private val analyzer = HeartRateAnalyzer()
    private val dataExporter = DataExporter()
    private val recordedData = mutableListOf<HeartRateData>()
    
    fun processFrame(rgbValues: FloatArray) {
        if (!_uiState.value.isRecording) return
        
        viewModelScope.launch {
            val timestamp = System.currentTimeMillis()
            val redValue = rgbValues[0] // 使用红色通道
            
            // 添加数据到分析器
            analyzer.addDataPoint(redValue, timestamp)
            
            // 记录原始数据
            recordedData.add(HeartRateData(timestamp, redValue))
            
            // 获取分析结果
            val result = analyzer.analyze()
            
            _uiState.value = _uiState.value.copy(
                heartRate = result.heartRate,
                status = result.status,
                waveformData = result.waveform
            )
        }
    }
    
    fun toggleRecording() {
        val newState = !_uiState.value.isRecording
        _uiState.value = _uiState.value.copy(
            isRecording = newState,
            status = if (newState) "测量中..." else "已停止"
        )
        
        if (newState) {
            analyzer.reset()
            recordedData.clear()
        }
    }
    
    fun exportData() {
        viewModelScope.launch {
            dataExporter.exportToCSV(recordedData)
        }
    }
}
