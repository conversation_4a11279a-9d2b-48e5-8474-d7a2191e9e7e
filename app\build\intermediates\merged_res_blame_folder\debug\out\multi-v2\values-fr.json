{"logs": [{"outputFile": "com.heartrate.monitor.app-mergeDebugResources-52:/values-fr/values-fr.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\4d13162283d09b9c9bedcbf64eea8d38\\transformed\\core-1.12.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,560,664,782", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "148,250,349,451,555,659,777,878"}, "to": {"startLines": "33,34,35,36,37,38,39,91", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3312,3410,3512,3611,3713,3817,3921,9372", "endColumns": "97,101,98,101,103,103,117,100", "endOffsets": "3405,3507,3606,3708,3812,3916,4034,9468"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\280b89a4d64ffddfbc411cbb93ddc07e\\transformed\\material3-1.1.2\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,171,285,403,520,595,685,794,934,1049,1192,1272,1369,1466,1563,1680,1803,1905,2051,2193,2319,2507,2629,2743,2863,2993,3091,3192,3312,3433,3531,3634,3735,3875,4023,4128,4232,4315,4392,4479,4562,4665,4741,4822,4920,5028,5122,5218,5302,5414,5511,5609,5737,5813,5919", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "166,280,398,515,590,680,789,929,1044,1187,1267,1364,1461,1558,1675,1798,1900,2046,2188,2314,2502,2624,2738,2858,2988,3086,3187,3307,3428,3526,3629,3730,3870,4018,4123,4227,4310,4387,4474,4557,4660,4736,4817,4915,5023,5117,5213,5297,5409,5506,5604,5732,5808,5914,6008"}, "to": {"startLines": "29,30,31,32,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,76,78,87,90,92,96,97,98,99,100,101,102,103,104,105,106,107,108,109", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2847,2963,3077,3195,4226,4301,4391,4500,4640,4755,4898,4978,5075,5172,5269,5386,5509,5611,5757,5899,6025,6213,6335,6449,6569,6699,6797,6898,7018,7139,7237,7340,7441,7581,7729,7834,8138,8308,9040,9289,9473,9845,9921,10002,10100,10208,10302,10398,10482,10594,10691,10789,10917,10993,11099", "endColumns": "115,113,117,116,74,89,108,139,114,142,79,96,96,96,116,122,101,145,141,125,187,121,113,119,129,97,100,119,120,97,102,100,139,147,104,103,82,76,86,82,102,75,80,97,107,93,95,83,111,96,97,127,75,105,93", "endOffsets": "2958,3072,3190,3307,4296,4386,4495,4635,4750,4893,4973,5070,5167,5264,5381,5504,5606,5752,5894,6020,6208,6330,6444,6564,6694,6792,6893,7013,7134,7232,7335,7436,7576,7724,7829,7933,8216,8380,9122,9367,9571,9916,9997,10095,10203,10297,10393,10477,10589,10686,10784,10912,10988,11094,11188"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\0c3d4fb4450e31bea660ef976ee110c7\\transformed\\appcompat-1.1.0\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,914,1005,1097,1195,1290,1391,1484,1577,1672,1763,1854,1939,2049,2160,2263,2374,2482,2589,2748,2847", "endColumns": "110,114,109,81,105,129,77,76,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,326,436,518,624,754,832,909,1000,1092,1190,1285,1386,1479,1572,1667,1758,1849,1934,2044,2155,2258,2369,2477,2584,2743,2842,2928"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,88", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,216,331,441,523,629,759,837,914,1005,1097,1195,1290,1391,1484,1577,1672,1763,1854,1939,2049,2160,2263,2374,2482,2589,2748,9127", "endColumns": "110,114,109,81,105,129,77,76,90,91,97,94,100,92,92,94,90,90,84,109,110,102,110,107,106,158,98,85", "endOffsets": "211,326,436,518,624,754,832,909,1000,1092,1190,1285,1386,1479,1572,1667,1758,1849,1934,2044,2155,2258,2369,2477,2584,2743,2842,9208"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-3\\8a7b4e9c2516b821e259ea96e27da3fe\\transformed\\ui-release\\res\\values-fr\\values-fr.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,204,292,392,492,579,658,750,842,929,1000,1068,1149,1234,1310,1388,1457", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "199,287,387,487,574,653,745,837,924,995,1063,1144,1229,1305,1383,1452,1574"}, "to": {"startLines": "40,41,74,75,77,79,80,81,82,83,84,85,86,89,93,94,95", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4039,4138,7938,8038,8221,8385,8464,8556,8648,8735,8806,8874,8955,9213,9576,9654,9723", "endColumns": "98,87,99,99,86,78,91,91,86,70,67,80,84,75,77,68,121", "endOffsets": "4133,4221,8033,8133,8303,8459,8551,8643,8730,8801,8869,8950,9035,9284,9649,9718,9840"}}]}]}