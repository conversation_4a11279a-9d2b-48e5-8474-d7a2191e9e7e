package com.heartrate.monitor.ui.components

import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Stop
import androidx.compose.material.icons.filled.FileDownload
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp

@Composable
fun MeasurementControls(
    isRecording: Boolean,
    onStartStop: () -> Unit,
    onExport: () -> Unit,
    modifier: Modifier = Modifier
) {
    Row(
        modifier = modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.SpaceEvenly,
        verticalAlignment = Alignment.CenterVertically
    ) {
        // 开始/停止按钮
        Button(
            onClick = onStartStop,
            colors = ButtonDefaults.buttonColors(
                containerColor = if (isRecording) 
                    MaterialTheme.colorScheme.error 
                else 
                    MaterialTheme.colorScheme.primary
            )
        ) {
            Icon(
                imageVector = if (isRecording) Icons.Default.Stop else Icons.Default.PlayArrow,
                contentDescription = if (isRecording) "停止测量" else "开始测量"
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text(
                text = if (isRecording) "停止测量" else "开始测量"
            )
        }
        
        // 导出数据按钮
        OutlinedButton(
            onClick = onExport,
            enabled = !isRecording
        ) {
            Icon(
                imageVector = Icons.Default.FileDownload,
                contentDescription = "导出数据"
            )
            Spacer(modifier = Modifier.width(8.dp))
            Text("导出数据")
        }
    }
}
